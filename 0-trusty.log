 RSA_hash
[   45.295090] c0 ta_manager_verify_img:506: RSA_verify
[   45.296670] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   45.296682] c0 trusty_app:(32) start 0xffffffffe090e000 size 0x001d4000
[   45.296955] c0 trusty_app: whitelist.table 0x0, size: 0
[   45.296959] c0 trusty_app 15 uuid: 0x81af0b44 0x41f0 0x11e7 0xa919 0x92ebcb67fe33
[   45.296967] c0 trusty_app 0xffffffffe0727e40: stack_sz=0x10000
[   45.296971] c0 trusty_app 0xffffffffe0727e40: heap_sz=0x800000
[   45.296974] c0 trusty_app 0xffffffffe0727e40: one_shot=0x0
[   45.296977] c0 trusty_app 0xffffffffe0727e40: keep_alive=0x1
[   45.296980] c0 trusty_app 0xffffffffe0727e40: flags=0x1c
[   45.296983] c0 ta_manager_write_ta:985: enter tam anti rollback
[   45.296990] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   45.296994] c0 ta_manager_write_ta:997: tam anti rollback ok
[   45.296997] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   45.297002] c0 trusty_tapp_init:
[   45.299125] c0 trusty_app 15: code: start 0x00008000 end 0x000a7ed4
[   45.299136] c0 trusty_app 15: data: start 0x000a8000 end 0x001d9000
[   45.299141] c0 trusty_app 15: bss:                end 0x001d8624
[   45.299144] c0 trusty_app 15: brk:  start 0x001d9000 end 0x009d9000
[   45.299148] c0 trusty_app 15: entry 0x0002f09c
[   45.299203] c0 tam_port_publish:1501:  other port com.android.trusty.sec.widevine
[   45.299221] c0 tam_port_publish:1496: publish port com.android.trusty.widevine
[   45.299232] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.widevine accomplished!
<   45.315015> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.315032> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.321720> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.321734> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.332206> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.332225> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.345405> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.345424> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.357718> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.357736> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.392283> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   45.392299> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.392312> ss-ipc: 185: do_disconnect ev->handle ox3f6
<   45.392318> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
[   45.395850] c2 tam_disc_notify:1554: port =  com.android.trusty.widevine
[   45.395862] c2 tam_disc_notify:1572: Keep alive TA will not disconnect.
<   45.432468> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432481> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432552> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432559> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432623> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432629> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432689> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432695> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432945> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432950> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.475848> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   45.475868> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.475884> ss-ipc: 185: do_disconnect ev->handle ox3f6
<   45.475892> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
< 1283.794396> trusty_gatekeeper_ta: 131: handle_request  GK_DELETE_USER
< 1283.794412> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.794457> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.794476> libhwkey: 148: data compare failed
< 1283.794628> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.794640> trusty_gatekeeper_ta: 219: DeleteUser >>>>>>>>>>
< 1283.794715> file_delete:1243: file 38ba0cdcdf0e11e49869233fb6ae4795/gatekeeper.100000 not found
< 1283.794736> trusty_gatekeeper_ta: 224: DeleteUser <<<<<<<<<<<
< 1283.794956> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.794967> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.799407> trusty_gatekeeper_ta: 123: handle_request  GK_ENROLL
< 1283.799426> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.799473> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.799493> libhwkey: 148: data compare failed
< 1283.799528> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.799539> trusty_gatekeeper_ta: 29: Enroll >>>>>>>>>>
< 1283.799549> trusty_gatekeeper_ta: 93: Enroll uid:100000, sid:9159674682746387616
< 1283.799567> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.810659> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1283.810690> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1283.810700> trusty_gatekeeper_ta: 400: Enroll to save Master[31]:192  Master[0]:26
< 1283.810709> trusty_gatekeeper_ta: 417: Enroll to save salted[88]:142  salted[0]:100
< 1283.810718> trusty_gatekeeper_ta: 420: Enroll to save signature[31]:96  signature[0]:173
< 1283.810730> trusty_gatekeeper_ta: 503: WriteEnrolledPasswordInfo
< 1283.810770> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.810787> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.843496> trusty_gatekeeper_ta: 116: Enroll <<<<<<<<<<<
< 1283.843697> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.843714> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.847777> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1283.847794> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.847840> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.847859> libhwkey: 148: data compare failed
< 1283.847892> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.847903> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1283.847910> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1283847 
< 1283.847919> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1283.847937> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1283.848091> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1283847, last_checked:0, failure_count:0, timeout:0
< 1283.848106> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.848131> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.848144> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.856192> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1283.856221> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1283.856230> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1283.856271> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1283.856280> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1283.856308> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1283.856317> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1283.856332> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.856372> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.856389> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.862226> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1283.862440> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.862455> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.949461> trusty_gatekeeper_ta: 123: handle_request  GK_ENROLL
< 1283.949477> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.949523> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.949542> libhwkey: 148: data compare failed
< 1283.949577> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.949587> trusty_gatekeeper_ta: 29: Enroll >>>>>>>>>>
< 1283.949596> trusty_gatekeeper_ta: 93: Enroll uid:0, sid:4912068877405679931
< 1283.949611> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.955733> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1283.955763> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1283.955773> trusty_gatekeeper_ta: 400: Enroll to save Master[31]:192  Master[0]:26
< 1283.955782> trusty_gatekeeper_ta: 417: Enroll to save salted[56]:112  salted[0]:107
< 1283.955791> trusty_gatekeeper_ta: 420: Enroll to save signature[31]:231  signature[0]:37
< 1283.955801> trusty_gatekeeper_ta: 503: WriteEnrolledPasswordInfo
< 1283.955840> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.955856> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.962555> trusty_gatekeeper_ta: 116: Enroll <<<<<<<<<<<
< 1283.962783> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.962798> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.963958> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1283.963972> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.964016> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.964035> libhwkey: 148: data compare failed
< 1283.964071> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.964084> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1283.964093> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1283964 
< 1283.964103> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1283.964123> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1283.964277> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1283964, last_checked:0, failure_count:0, timeout:0
< 1283.964292> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.964319> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.964334> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.974124> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1283.974156> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1283.974166> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1283.974206> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1283.974215> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1283.974242> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1283.974249> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1283.974265> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.974305> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.974322> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.979924> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1283.980127> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.980143> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.986239> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1283.986254> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.986300> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.986318> libhwkey: 148: data compare failed
< 1283.986351> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.986362> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1283.986370> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1283986 
< 1283.986380> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1283.986397> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1283.986546> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1283986, last_checked:0, failure_count:0, timeout:0
< 1283.986561> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.986588> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.986602> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.993682> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1283.993710> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1283.993720> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1283.993759> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1283.993766> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1283.993791> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1283.993798> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1283.993812> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.993850> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.993863> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1284.000289> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1284.000508> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1284.000524> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.366790> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1372.366807> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1372.366853> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1372.366872> libhwkey: 148: data compare failed
< 1372.366907> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1372.366918> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1372.366925> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1372366 
< 1372.366934> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1372.366952> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1372.367115> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1372366, last_checked:0, failure_count:0, timeout:0
< 1372.367130> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.367156> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.367168> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.374379> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1372.374413> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1372.374422> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1372.374469> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1372.374477> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1372.374504> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1372.374511> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1372.374526> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.374568> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.374586> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.388975> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1372.389226> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.389242> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.407582> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1372.407599> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1372.407648> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1372.407666> libhwkey: 148: data compare failed
< 1372.407700> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1372.407711> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1372.407718> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1372407 
< 1372.407727> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1372.407744> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1372.407909> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1372407, last_checked:0, failure_count:0, timeout:0
< 1372.407922> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.407949> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.407961> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.418875> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1372.418906> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1372.419022> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1372.419073> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1372.419081> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1372.419109> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1372.419116> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1372.419134> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.419178> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.419193> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.443131> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1372.443316> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.443328> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.468432> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1372.468551> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1372.468587> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1372.468601> libhwkey: 148: data compare failed
< 1372.468626> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1372.468635> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1372.468641> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1372468 
< 1372.468648> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1372.468663> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1372.469107> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1372468, last_checked:0, failure_count:0, timeout:0
< 1372.469122> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.469150> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.469161> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.482187> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1372.482220> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1372.482229> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1372.482274> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1372.482282> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1372.482307> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1372.482315> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1372.482328> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.482367> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.482382> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.498043> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1372.501742> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.501765> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.108924> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1431.108941> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1431.108990> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1431.109009> libhwkey: 148: data compare failed
< 1431.109044> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1431.109132> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1431.109141> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1431109 
< 1431.109150> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1431.109168> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1431.109339> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1431109, last_checked:0, failure_count:0, timeout:0
< 1431.109354> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.109379> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.109393> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.117327> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1431.117358> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1431.117367> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1431.117480> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1431.117489> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1431.117518> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1431.117526> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1431.117543> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.117588> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.117605> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.123865> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1431.124080> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.124096> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.138242> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1431.138259> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1431.138308> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1431.138328> libhwkey: 148: data compare failed
< 1431.138363> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1431.138374> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1431.138382> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1431138 
< 1431.138390> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1431.138407> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1431.138569> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1431138, last_checked:0, failure_count:0, timeout:0
< 1431.138581> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.138608> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.138620> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.153238> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1431.153271> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1431.153280> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1431.153324> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1431.153332> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1431.153358> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1431.153365> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1431.153379> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.153739> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.153758> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.188797> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1431.189045> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.189060> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.203385> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1431.203403> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1431.203451> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1431.203469> libhwkey: 148: data compare failed
< 1431.203503> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1431.203513> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1431.203521> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1431203 
< 1431.203529> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1431.203546> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1431.203700> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1431203, last_checked:0, failure_count:0, timeout:0
< 1431.203712> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.203737> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.203749> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.212353> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1431.212383> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1431.212391> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1431.212434> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1431.212441> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1431.212466> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1431.212473> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1431.212486> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.212525> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.212539> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.221128> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1431.221371> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.221386> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.059113> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1526.059132> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1526.059185> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1526.059206> libhwkey: 148: data compare failed
< 1526.059244> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1526.059256> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1526.059263> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1526059 
< 1526.059272> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1526.059289> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1526.059465> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1526059, last_checked:0, failure_count:0, timeout:0
< 1526.059481> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.059507> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.059520> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.066245> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1526.066278> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1526.066287> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1526.066333> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1526.066341> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1526.066368> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1526.066375> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1526.066389> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.066431> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.066448> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.076169> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1526.076535> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.076552> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.094242> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1526.094260> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1526.094361> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1526.094381> libhwkey: 148: data compare failed
< 1526.094416> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1526.094426> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1526.094434> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1526094 
< 1526.094442> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1526.094458> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1526.096064> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1526094, last_checked:0, failure_count:0, timeout:0
< 1526.096085> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.096130> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.096144> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.131909> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1526.131939> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1526.131948> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1526.131988> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1526.131996> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1526.132022> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1526.132029> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1526.132042> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.132081> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.132097> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.145602> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1526.145845> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.145860> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.153871> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1526.153889> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1526.153938> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1526.153957> libhwkey: 148: data compare failed
< 1526.153991> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1526.154002> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1526.154009> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1526154 
< 1526.154018> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1526.154034> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1526.154192> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1526154, last_checked:0, failure_count:0, timeout:0
< 1526.154205> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.154232> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.154244> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.162488> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1526.162524> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1526.162534> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1526.162583> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1526.162590> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1526.162619> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1526.162626> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1526.162640> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.162682> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.162699> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.169990> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1526.170227> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.170241> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.628776> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1867.628797> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1867.628851> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1867.628873> libhwkey: 148: data compare failed
< 1867.628912> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1867.628925> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1867.628934> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1867628 
< 1867.628944> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1867.628966> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1867.629147> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1867628, last_checked:0, failure_count:0, timeout:0
< 1867.629162> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.629190> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.629205> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.638364> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1867.638398> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1867.638409> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1867.638456> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1867.638465> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1867.638493> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1867.638502> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1867.638518> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.638560> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.638578> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.644494> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1867.644710> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.644727> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.657801> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1867.657820> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1867.657872> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1867.657893> libhwkey: 148: data compare failed
< 1867.657931> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1867.657943> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1867.657951> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1867657 
< 1867.657960> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1867.657977> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1867.658151> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1867657, last_checked:0, failure_count:0, timeout:0
< 1867.658164> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.658190> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.658204> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.665915> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1867.665953> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1867.665962> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1867.666014> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1867.666022> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1867.666050> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1867.666057> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1867.666071> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.666114> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.666132> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.673334> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1867.673640> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.673658> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.681631> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1867.681651> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1867.681705> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1867.681726> libhwkey: 148: data compare failed
< 1867.681764> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1867.681776> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1867.681784> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1867681 
< 1867.681794> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1867.681810> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1867.681984> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1867681, last_checked:0, failure_count:0, timeout:0
< 1867.681997> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.682024> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.682038> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.689295> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1867.689333> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1867.689348> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1867.689397> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1867.689409> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1867.689447> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1867.689459> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1867.689479> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.689581> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.689603> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.697939> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1867.698661> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.698713> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
< 1917.718101> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1917.718115> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1917.718150> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1917.718165> libhwkey: 148: data compare failed
< 1917.718191> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1917.718199> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1917.718205> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1917718 
< 1917.718212> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1917.718228> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1917.718341> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1917718, last_checked:0, failure_count:0, timeout:0
< 1917.718351> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.718368> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.718378> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.729055> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1917.729078> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1917.729085> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1917.729114> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1917.729120> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1917.729140> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1917.729145> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1917.729155> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.729180> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.729192> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.738863> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1917.739044> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.739056> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.785369> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1917.785385> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1917.785419> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1917.785434> libhwkey: 148: data compare failed
< 1917.785460> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1917.785468> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1917.785474> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1917785 
< 1917.785481> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1917.785496> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1917.785611> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1917785, last_checked:0, failure_count:0, timeout:0
< 1917.785622> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.785639> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.785649> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.791894> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1917.792228> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1917.792502> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1917.792562> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1917.792570> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1917.792606> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1917.792613> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1917.792634> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.792681> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.792698> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.800380> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1917.800638> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.800655> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.816743> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1917.816761> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1917.816808> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1917.816827> libhwkey: 148: data compare failed
< 1917.816863> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1917.816874> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1917.816881> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1917816 
< 1917.816890> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1917.816906> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1917.817068> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1917816, last_checked:0, failure_count:0, timeout:0
< 1917.817082> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.817109> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.817121> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.827827> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1917.827857> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1917.827866> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1917.827909> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1917.827917> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1917.827942> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1917.827949> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1917.827963> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.828004> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.828019> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.838489> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1917.838741> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.838757> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
< 1980.371844> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1980.371864> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1980.371917> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1980.371936> libhwkey: 148: data compare failed
< 1980.371975> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1980.371987> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1980.371995> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1980371 
< 1980.372005> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1980.372026> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1980.372207> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1980371, last_checked:0, failure_count:0, timeout:0
< 1980.372223> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1980.372312> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1980.372326> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1980.382141> trusty_gatekeeper_ta: 214: salted[88]:25  salted[0]:100
< 1980.382177> trusty_gatekeeper_ta: 217: signature[31]:4  signature[0]:148
< 1980.382187> trusty_gatekeeper_ta: 188: Verify GetEnrolledPasswordInfo uid:100000, sid:9159674682746387616
< 1980.382246> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1980.382263> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1980.382403> trusty_gatekeeper_ta: 192: saved sid:9159674682746387616
< 1980.382425> trusty_gatekeeper_ta: 193: saved uid:100000
< 1980.382433> trusty_gatekeeper_ta: 194: saved salted_pwd_len:89
< 1980.382441> trusty_gatekeeper_ta: 198: saved salted[88]:142  salted[0]:100
< 1980.382451> trusty_gatekeeper_ta: 203: saved signature[31]:96  signature[0]:173
< 1980.382460> trusty_gatekeeper_ta: 205: saved Master[31]:192  Master[0]:26
< 1980.382469> trusty_gatekeeper_ta: 213: Verify fail <<<<<<<
< 1980.382688> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1980.382699> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.097207> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2059.097227> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2059.097278> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2059.097299> libhwkey: 148: data compare failed
< 2059.097336> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2059.097347> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2059.097355> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2059097 
< 2059.097363> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2059.097381> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2059.097560> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2059097, last_checked:1980371, failure_count:1, timeout:0
< 2059.097575> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.097601> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.097614> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.105496> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2059.105530> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2059.105539> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2059.105585> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2059.105593> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2059.105620> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2059.105627> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2059.105642> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.105686> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.105702> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.113150> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2059.113394> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.113411> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.162578> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2059.162596> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2059.162722> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2059.162743> libhwkey: 148: data compare failed
< 2059.162778> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2059.162790> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2059.162797> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2059162 
< 2059.162806> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2059.162822> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2059.162996> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2059162, last_checked:0, failure_count:0, timeout:0
< 2059.163010> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.163038> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.163052> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.198274> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2059.198305> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2059.198315> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2059.198359> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2059.198367> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2059.198392> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2059.198399> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2059.198412> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.198452> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.198466> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.207595> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2059.208266> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.208286> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.217354> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2059.217371> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2059.217417> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2059.217436> libhwkey: 148: data compare failed
< 2059.217473> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2059.217484> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2059.217491> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2059217 
< 2059.217500> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2059.217517> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2059.217679> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2059217, last_checked:0, failure_count:0, timeout:0
< 2059.217693> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.217721> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.217734> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.235825> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2059.235847> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2059.235854> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2059.235883> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2059.235889> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2059.235908> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2059.235914> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2059.235923> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.235948> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.235960> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.251273> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2059.251513> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.251529> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.831484> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2456.831504> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2456.831560> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2456.831581> libhwkey: 148: data compare failed
< 2456.831619> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2456.831632> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2456.831640> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2456831 
< 2456.831649> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2456.831668> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2456.831846> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2456831, last_checked:0, failure_count:0, timeout:0
< 2456.831861> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.831887> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.831901> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.842327> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2456.842363> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2456.842372> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2456.842420> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2456.842428> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2456.842456> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2456.842464> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2456.842478> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.842521> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.842538> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.851580> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2456.851814> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.851830> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.868503> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2456.868520> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2456.868568> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2456.868587> libhwkey: 148: data compare failed
< 2456.868621> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2456.868632> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2456.868640> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2456868 
< 2456.868734> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2456.868751> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2456.868916> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2456868, last_checked:0, failure_count:0, timeout:0
< 2456.868930> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.868955> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.868969> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.881215> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2456.881246> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2456.881255> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2456.881298> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2456.881306> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2456.881332> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2456.881339> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2456.881353> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.881390> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.881406> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.918483> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2456.918715> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.918730> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.930282> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2456.930300> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2456.930389> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2456.930410> libhwkey: 148: data compare failed
< 2456.930445> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2456.930456> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2456.930464> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2456930 
< 2456.930473> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2456.930490> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2456.930912> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2456930, last_checked:0, failure_count:0, timeout:0
< 2456.930931> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.930971> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.930986> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.942255> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2456.942286> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2456.942295> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2456.942338> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2456.942345> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2456.942370> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2456.942378> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2456.942391> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.942431> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.942446> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.951766> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2456.952006> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.952021> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.749936> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2499.749954> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2499.750004> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2499.750104> libhwkey: 148: data compare failed
< 2499.750152> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2499.750170> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2499.750183> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2499750 
< 2499.750193> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2499.750212> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2499.750377> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2499750, last_checked:0, failure_count:0, timeout:0
< 2499.750394> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.750420> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.750432> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.756350> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2499.756385> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2499.756395> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2499.756442> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2499.756449> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2499.756476> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2499.756483> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2499.756498> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.756541> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.756558> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.763944> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2499.764165> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.764183> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.781916> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2499.781933> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2499.781980> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2499.781999> libhwkey: 148: data compare failed
< 2499.782033> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2499.782043> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2499.782051> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2499782 
< 2499.782060> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2499.782077> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2499.782235> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2499782, last_checked:0, failure_count:0, timeout:0
< 2499.782248> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.782273> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.782285> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.789340> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2499.789371> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2499.789380> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2499.789423> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2499.789431> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2499.789457> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2499.789465> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2499.789479> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.789518> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.789534> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.796331> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2499.796550> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.796566> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.817552> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2499.817569> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2499.817618> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2499.817637> libhwkey: 148: data compare failed
< 2499.817671> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2499.817683> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2499.817691> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2499817 
< 2499.817700> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2499.817716> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2499.817905> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2499817, last_checked:0, failure_count:0, timeout:0
< 2499.817918> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.817945> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.817958> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.828312> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2499.828348> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2499.828358> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2499.828402> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2499.828409> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2499.828435> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2499.828442> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2499.828456> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.828496> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.828513> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.845288> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2499.845554> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.845571> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.375863> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2520.375883> trusty_gatekeeper_ta: 89: ReseedRng
< 2520.375895> trusty_gatekeeper_ta: 97: ReseedRng ok
< 2520.375902> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2520.375958> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2520.375977> libhwkey: 148: data compare failed
< 2520.376014> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2520.376026> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2520.376033> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2520376 
< 2520.376042> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2520.376060> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2520.376238> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2520376, last_checked:0, failure_count:0, timeout:0
< 2520.376252> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.376279> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.376293> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.383526> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2520.383564> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2520.383574> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2520.383619> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2520.383626> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2520.383652> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2520.383660> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2520.383674> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.383716> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.383734> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.388632> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2520.388881> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.388897> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.405518> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2520.405536> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2520.405583> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2520.405601> libhwkey: 148: data compare failed
< 2520.405634> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2520.405645> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2520.405652> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2520405 
< 2520.405661> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2520.405678> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2520.405833> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2520405, last_checked:0, failure_count:0, timeout:0
< 2520.405847> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.405872> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.405885> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.413485> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2520.413516> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2520.413525> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2520.413568> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2520.413575> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2520.413601> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2520.413608> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2520.413621> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.413661> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.413676> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.431338> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2520.431576> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.431591> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.442569> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2520.442587> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2520.442634> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2520.442653> libhwkey: 148: data compare failed
< 2520.442688> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2520.442699> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2520.442706> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2520442 
< 2520.442715> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2520.442732> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2520.442892> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2520442, last_checked:0, failure_count:0, timeout:0
< 2520.442906> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.442933> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.442947> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.489417> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2520.489450> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2520.489459> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2520.489505> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2520.489512> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2520.489539> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2520.489546> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2520.489560> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.489602> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.489618> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.501550> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2520.501795> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.501811> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
 RSA_hash
[   45.295090] c0 ta_manager_verify_img:506: RSA_verify
[   45.296670] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   45.296682] c0 trusty_app:(32) start 0xffffffffe090e000 size 0x001d4000
[   45.296955] c0 trusty_app: whitelist.table 0x0, size: 0
[   45.296959] c0 trusty_app 15 uuid: 0x81af0b44 0x41f0 0x11e7 0xa919 0x92ebcb67fe33
[   45.296967] c0 trusty_app 0xffffffffe0727e40: stack_sz=0x10000
[   45.296971] c0 trusty_app 0xffffffffe0727e40: heap_sz=0x800000
[   45.296974] c0 trusty_app 0xffffffffe0727e40: one_shot=0x0
[   45.296977] c0 trusty_app 0xffffffffe0727e40: keep_alive=0x1
[   45.296980] c0 trusty_app 0xffffffffe0727e40: flags=0x1c
[   45.296983] c0 ta_manager_write_ta:985: enter tam anti rollback
[   45.296990] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   45.296994] c0 ta_manager_write_ta:997: tam anti rollback ok
[   45.296997] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   45.297002] c0 trusty_tapp_init:
[   45.299125] c0 trusty_app 15: code: start 0x00008000 end 0x000a7ed4
[   45.299136] c0 trusty_app 15: data: start 0x000a8000 end 0x001d9000
[   45.299141] c0 trusty_app 15: bss:                end 0x001d8624
[   45.299144] c0 trusty_app 15: brk:  start 0x001d9000 end 0x009d9000
[   45.299148] c0 trusty_app 15: entry 0x0002f09c
[   45.299203] c0 tam_port_publish:1501:  other port com.android.trusty.sec.widevine
[   45.299221] c0 tam_port_publish:1496: publish port com.android.trusty.widevine
[   45.299232] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.widevine accomplished!
<   45.315015> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.315032> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.321720> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.321734> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.332206> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.332225> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.345405> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.345424> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.357718> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.357736> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.392283> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   45.392299> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.392312> ss-ipc: 185: do_disconnect ev->handle ox3f6
<   45.392318> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
[   45.395850] c2 tam_disc_notify:1554: port =  com.android.trusty.widevine
[   45.395862] c2 tam_disc_notify:1572: Keep alive TA will not disconnect.
<   45.432468> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432481> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432552> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432559> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432623> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432629> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432689> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432695> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.432945> ss-ipc: 185: do_disconnect ev->handle ox3f7
<   45.432950> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.475848> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   45.475868> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<   45.475884> ss-ipc: 185: do_disconnect ev->handle ox3f6
<   45.475892> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
< 1283.794396> trusty_gatekeeper_ta: 131: handle_request  GK_DELETE_USER
< 1283.794412> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.794457> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.794476> libhwkey: 148: data compare failed
< 1283.794628> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.794640> trusty_gatekeeper_ta: 219: DeleteUser >>>>>>>>>>
< 1283.794715> file_delete:1243: file 38ba0cdcdf0e11e49869233fb6ae4795/gatekeeper.100000 not found
< 1283.794736> trusty_gatekeeper_ta: 224: DeleteUser <<<<<<<<<<<
< 1283.794956> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.794967> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.799407> trusty_gatekeeper_ta: 123: handle_request  GK_ENROLL
< 1283.799426> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.799473> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.799493> libhwkey: 148: data compare failed
< 1283.799528> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.799539> trusty_gatekeeper_ta: 29: Enroll >>>>>>>>>>
< 1283.799549> trusty_gatekeeper_ta: 93: Enroll uid:100000, sid:9159674682746387616
< 1283.799567> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.810659> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1283.810690> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1283.810700> trusty_gatekeeper_ta: 400: Enroll to save Master[31]:192  Master[0]:26
< 1283.810709> trusty_gatekeeper_ta: 417: Enroll to save salted[88]:142  salted[0]:100
< 1283.810718> trusty_gatekeeper_ta: 420: Enroll to save signature[31]:96  signature[0]:173
< 1283.810730> trusty_gatekeeper_ta: 503: WriteEnrolledPasswordInfo
< 1283.810770> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.810787> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.843496> trusty_gatekeeper_ta: 116: Enroll <<<<<<<<<<<
< 1283.843697> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.843714> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.847777> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1283.847794> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.847840> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.847859> libhwkey: 148: data compare failed
< 1283.847892> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.847903> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1283.847910> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1283847 
< 1283.847919> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1283.847937> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1283.848091> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1283847, last_checked:0, failure_count:0, timeout:0
< 1283.848106> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.848131> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.848144> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.856192> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1283.856221> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1283.856230> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1283.856271> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1283.856280> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1283.856308> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1283.856317> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1283.856332> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.856372> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.856389> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.862226> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1283.862440> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.862455> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.949461> trusty_gatekeeper_ta: 123: handle_request  GK_ENROLL
< 1283.949477> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.949523> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.949542> libhwkey: 148: data compare failed
< 1283.949577> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.949587> trusty_gatekeeper_ta: 29: Enroll >>>>>>>>>>
< 1283.949596> trusty_gatekeeper_ta: 93: Enroll uid:0, sid:4912068877405679931
< 1283.949611> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.955733> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1283.955763> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1283.955773> trusty_gatekeeper_ta: 400: Enroll to save Master[31]:192  Master[0]:26
< 1283.955782> trusty_gatekeeper_ta: 417: Enroll to save salted[56]:112  salted[0]:107
< 1283.955791> trusty_gatekeeper_ta: 420: Enroll to save signature[31]:231  signature[0]:37
< 1283.955801> trusty_gatekeeper_ta: 503: WriteEnrolledPasswordInfo
< 1283.955840> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.955856> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.962555> trusty_gatekeeper_ta: 116: Enroll <<<<<<<<<<<
< 1283.962783> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.962798> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.963958> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1283.963972> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.964016> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.964035> libhwkey: 148: data compare failed
< 1283.964071> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.964084> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1283.964093> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1283964 
< 1283.964103> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1283.964123> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1283.964277> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1283964, last_checked:0, failure_count:0, timeout:0
< 1283.964292> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.964319> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.964334> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.974124> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1283.974156> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1283.974166> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1283.974206> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1283.974215> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1283.974242> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1283.974249> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1283.974265> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.974305> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.974322> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.979924> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1283.980127> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.980143> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.986239> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1283.986254> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1283.986300> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1283.986318> libhwkey: 148: data compare failed
< 1283.986351> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1283.986362> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1283.986370> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1283986 
< 1283.986380> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1283.986397> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1283.986546> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1283986, last_checked:0, failure_count:0, timeout:0
< 1283.986561> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.986588> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.986602> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1283.993682> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1283.993710> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1283.993720> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1283.993759> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1283.993766> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1283.993791> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1283.993798> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1283.993812> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1283.993850> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1283.993863> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1284.000289> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1284.000508> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1284.000524> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.366790> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1372.366807> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1372.366853> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1372.366872> libhwkey: 148: data compare failed
< 1372.366907> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1372.366918> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1372.366925> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1372366 
< 1372.366934> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1372.366952> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1372.367115> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1372366, last_checked:0, failure_count:0, timeout:0
< 1372.367130> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.367156> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.367168> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.374379> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1372.374413> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1372.374422> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1372.374469> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1372.374477> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1372.374504> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1372.374511> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1372.374526> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.374568> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.374586> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.388975> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1372.389226> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.389242> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.407582> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1372.407599> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1372.407648> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1372.407666> libhwkey: 148: data compare failed
< 1372.407700> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1372.407711> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1372.407718> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1372407 
< 1372.407727> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1372.407744> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1372.407909> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1372407, last_checked:0, failure_count:0, timeout:0
< 1372.407922> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.407949> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.407961> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.418875> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1372.418906> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1372.419022> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1372.419073> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1372.419081> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1372.419109> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1372.419116> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1372.419134> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.419178> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.419193> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.443131> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1372.443316> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.443328> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.468432> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1372.468551> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1372.468587> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1372.468601> libhwkey: 148: data compare failed
< 1372.468626> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1372.468635> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1372.468641> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1372468 
< 1372.468648> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1372.468663> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1372.469107> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1372468, last_checked:0, failure_count:0, timeout:0
< 1372.469122> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.469150> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.469161> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.482187> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1372.482220> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1372.482229> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1372.482274> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1372.482282> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1372.482307> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1372.482315> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1372.482328> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1372.482367> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.482382> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1372.498043> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1372.501742> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1372.501765> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.108924> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1431.108941> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1431.108990> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1431.109009> libhwkey: 148: data compare failed
< 1431.109044> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1431.109132> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1431.109141> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1431109 
< 1431.109150> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1431.109168> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1431.109339> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1431109, last_checked:0, failure_count:0, timeout:0
< 1431.109354> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.109379> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.109393> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.117327> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1431.117358> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1431.117367> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1431.117480> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1431.117489> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1431.117518> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1431.117526> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1431.117543> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.117588> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.117605> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.123865> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1431.124080> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.124096> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.138242> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1431.138259> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1431.138308> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1431.138328> libhwkey: 148: data compare failed
< 1431.138363> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1431.138374> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1431.138382> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1431138 
< 1431.138390> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1431.138407> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1431.138569> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1431138, last_checked:0, failure_count:0, timeout:0
< 1431.138581> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.138608> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.138620> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.153238> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1431.153271> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1431.153280> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1431.153324> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1431.153332> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1431.153358> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1431.153365> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1431.153379> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.153739> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.153758> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.188797> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1431.189045> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.189060> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.203385> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1431.203403> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1431.203451> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1431.203469> libhwkey: 148: data compare failed
< 1431.203503> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1431.203513> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1431.203521> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1431203 
< 1431.203529> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1431.203546> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1431.203700> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1431203, last_checked:0, failure_count:0, timeout:0
< 1431.203712> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.203737> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.203749> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.212353> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1431.212383> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1431.212391> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1431.212434> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1431.212441> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1431.212466> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1431.212473> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1431.212486> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1431.212525> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.212539> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1431.221128> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1431.221371> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1431.221386> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.059113> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1526.059132> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1526.059185> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1526.059206> libhwkey: 148: data compare failed
< 1526.059244> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1526.059256> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1526.059263> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1526059 
< 1526.059272> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1526.059289> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1526.059465> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1526059, last_checked:0, failure_count:0, timeout:0
< 1526.059481> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.059507> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.059520> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.066245> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1526.066278> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1526.066287> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1526.066333> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1526.066341> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1526.066368> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1526.066375> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1526.066389> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.066431> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.066448> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.076169> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1526.076535> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.076552> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.094242> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1526.094260> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1526.094361> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1526.094381> libhwkey: 148: data compare failed
< 1526.094416> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1526.094426> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1526.094434> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1526094 
< 1526.094442> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1526.094458> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1526.096064> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1526094, last_checked:0, failure_count:0, timeout:0
< 1526.096085> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.096130> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.096144> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.131909> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1526.131939> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1526.131948> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1526.131988> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1526.131996> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1526.132022> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1526.132029> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1526.132042> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.132081> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.132097> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.145602> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1526.145845> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.145860> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.153871> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1526.153889> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1526.153938> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1526.153957> libhwkey: 148: data compare failed
< 1526.153991> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1526.154002> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1526.154009> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1526154 
< 1526.154018> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1526.154034> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1526.154192> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1526154, last_checked:0, failure_count:0, timeout:0
< 1526.154205> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.154232> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.154244> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.162488> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1526.162524> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1526.162534> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1526.162583> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1526.162590> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1526.162619> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1526.162626> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1526.162640> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1526.162682> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.162699> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1526.169990> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1526.170227> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1526.170241> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.628776> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1867.628797> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1867.628851> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1867.628873> libhwkey: 148: data compare failed
< 1867.628912> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1867.628925> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1867.628934> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1867628 
< 1867.628944> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1867.628966> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1867.629147> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1867628, last_checked:0, failure_count:0, timeout:0
< 1867.629162> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.629190> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.629205> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.638364> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1867.638398> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1867.638409> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1867.638456> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1867.638465> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1867.638493> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1867.638502> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1867.638518> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.638560> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.638578> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.644494> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1867.644710> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.644727> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.657801> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1867.657820> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1867.657872> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1867.657893> libhwkey: 148: data compare failed
< 1867.657931> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1867.657943> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1867.657951> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1867657 
< 1867.657960> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1867.657977> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1867.658151> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1867657, last_checked:0, failure_count:0, timeout:0
< 1867.658164> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.658190> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.658204> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.665915> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1867.665953> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1867.665962> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1867.666014> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1867.666022> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1867.666050> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1867.666057> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1867.666071> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.666114> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.666132> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.673334> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1867.673640> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.673658> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.681631> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1867.681651> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1867.681705> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1867.681726> libhwkey: 148: data compare failed
< 1867.681764> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1867.681776> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1867.681784> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1867681 
< 1867.681794> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1867.681810> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1867.681984> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1867681, last_checked:0, failure_count:0, timeout:0
< 1867.681997> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.682024> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.682038> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.689295> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1867.689333> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1867.689348> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1867.689397> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1867.689409> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1867.689447> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1867.689459> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1867.689479> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1867.689581> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.689603> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1867.697939> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1867.698661> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1867.698713> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
< 1917.718101> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1917.718115> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1917.718150> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1917.718165> libhwkey: 148: data compare failed
< 1917.718191> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1917.718199> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1917.718205> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1917718 
< 1917.718212> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1917.718228> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1917.718341> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1917718, last_checked:0, failure_count:0, timeout:0
< 1917.718351> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.718368> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.718378> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.729055> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 1917.729078> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 1917.729085> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1917.729114> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1917.729120> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1917.729140> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1917.729145> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1917.729155> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.729180> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.729192> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.738863> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1917.739044> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.739056> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.785369> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1917.785385> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1917.785419> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1917.785434> libhwkey: 148: data compare failed
< 1917.785460> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1917.785468> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1917.785474> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1917785 
< 1917.785481> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1917.785496> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1917.785611> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1917785, last_checked:0, failure_count:0, timeout:0
< 1917.785622> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.785639> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.785649> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.791894> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1917.792228> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1917.792502> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1917.792562> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1917.792570> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1917.792606> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1917.792613> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1917.792634> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.792681> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.792698> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.800380> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1917.800638> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.800655> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.816743> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1917.816761> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1917.816808> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1917.816827> libhwkey: 148: data compare failed
< 1917.816863> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1917.816874> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1917.816881> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1917816 
< 1917.816890> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 1917.816906> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1917.817068> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:1917816, last_checked:0, failure_count:0, timeout:0
< 1917.817082> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.817109> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.817121> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.827827> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 1917.827857> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 1917.827866> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 1917.827909> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 1917.827917> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 1917.827942> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 1917.827949> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 1917.827963> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1917.828004> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.828019> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1917.838489> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 1917.838741> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1917.838757> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
< 1980.371844> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 1980.371864> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 1980.371917> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 1980.371936> libhwkey: 148: data compare failed
< 1980.371975> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 1980.371987> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 1980.371995> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):1980371 
< 1980.372005> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 1980.372026> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 1980.372207> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:1980371, last_checked:0, failure_count:0, timeout:0
< 1980.372223> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 1980.372312> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1980.372326> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1980.382141> trusty_gatekeeper_ta: 214: salted[88]:25  salted[0]:100
< 1980.382177> trusty_gatekeeper_ta: 217: signature[31]:4  signature[0]:148
< 1980.382187> trusty_gatekeeper_ta: 188: Verify GetEnrolledPasswordInfo uid:100000, sid:9159674682746387616
< 1980.382246> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1980.382263> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 1980.382403> trusty_gatekeeper_ta: 192: saved sid:9159674682746387616
< 1980.382425> trusty_gatekeeper_ta: 193: saved uid:100000
< 1980.382433> trusty_gatekeeper_ta: 194: saved salted_pwd_len:89
< 1980.382441> trusty_gatekeeper_ta: 198: saved salted[88]:142  salted[0]:100
< 1980.382451> trusty_gatekeeper_ta: 203: saved signature[31]:96  signature[0]:173
< 1980.382460> trusty_gatekeeper_ta: 205: saved Master[31]:192  Master[0]:26
< 1980.382469> trusty_gatekeeper_ta: 213: Verify fail <<<<<<<
< 1980.382688> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 1980.382699> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.097207> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2059.097227> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2059.097278> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2059.097299> libhwkey: 148: data compare failed
< 2059.097336> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2059.097347> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2059.097355> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2059097 
< 2059.097363> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2059.097381> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2059.097560> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2059097, last_checked:1980371, failure_count:1, timeout:0
< 2059.097575> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.097601> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.097614> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.105496> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2059.105530> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2059.105539> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2059.105585> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2059.105593> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2059.105620> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2059.105627> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2059.105642> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.105686> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.105702> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.113150> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2059.113394> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.113411> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.162578> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2059.162596> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2059.162722> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2059.162743> libhwkey: 148: data compare failed
< 2059.162778> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2059.162790> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2059.162797> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2059162 
< 2059.162806> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2059.162822> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2059.162996> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2059162, last_checked:0, failure_count:0, timeout:0
< 2059.163010> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.163038> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.163052> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.198274> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2059.198305> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2059.198315> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2059.198359> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2059.198367> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2059.198392> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2059.198399> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2059.198412> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.198452> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.198466> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.207595> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2059.208266> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.208286> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.217354> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2059.217371> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2059.217417> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2059.217436> libhwkey: 148: data compare failed
< 2059.217473> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2059.217484> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2059.217491> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2059217 
< 2059.217500> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2059.217517> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2059.217679> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2059217, last_checked:0, failure_count:0, timeout:0
< 2059.217693> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.217721> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.217734> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.235825> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2059.235847> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2059.235854> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2059.235883> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2059.235889> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2059.235908> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2059.235914> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2059.235923> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2059.235948> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.235960> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2059.251273> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2059.251513> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2059.251529> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.831484> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2456.831504> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2456.831560> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2456.831581> libhwkey: 148: data compare failed
< 2456.831619> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2456.831632> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2456.831640> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2456831 
< 2456.831649> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2456.831668> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2456.831846> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2456831, last_checked:0, failure_count:0, timeout:0
< 2456.831861> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.831887> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.831901> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.842327> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2456.842363> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2456.842372> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2456.842420> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2456.842428> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2456.842456> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2456.842464> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2456.842478> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.842521> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.842538> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.851580> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2456.851814> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.851830> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.868503> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2456.868520> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2456.868568> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2456.868587> libhwkey: 148: data compare failed
< 2456.868621> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2456.868632> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2456.868640> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2456868 
< 2456.868734> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2456.868751> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2456.868916> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2456868, last_checked:0, failure_count:0, timeout:0
< 2456.868930> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.868955> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.868969> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.881215> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2456.881246> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2456.881255> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2456.881298> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2456.881306> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2456.881332> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2456.881339> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2456.881353> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.881390> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.881406> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.918483> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2456.918715> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.918730> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.930282> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2456.930300> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2456.930389> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2456.930410> libhwkey: 148: data compare failed
< 2456.930445> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2456.930456> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2456.930464> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2456930 
< 2456.930473> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2456.930490> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2456.930912> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2456930, last_checked:0, failure_count:0, timeout:0
< 2456.930931> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.930971> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.930986> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.942255> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2456.942286> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2456.942295> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2456.942338> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2456.942345> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2456.942370> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2456.942378> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2456.942391> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2456.942431> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.942446> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2456.951766> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2456.952006> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2456.952021> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.749936> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2499.749954> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2499.750004> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2499.750104> libhwkey: 148: data compare failed
< 2499.750152> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2499.750170> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2499.750183> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2499750 
< 2499.750193> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2499.750212> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2499.750377> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2499750, last_checked:0, failure_count:0, timeout:0
< 2499.750394> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.750420> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.750432> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.756350> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2499.756385> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2499.756395> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2499.756442> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2499.756449> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2499.756476> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2499.756483> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2499.756498> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.756541> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.756558> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.763944> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2499.764165> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.764183> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.781916> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2499.781933> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2499.781980> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2499.781999> libhwkey: 148: data compare failed
< 2499.782033> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2499.782043> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2499.782051> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2499782 
< 2499.782060> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2499.782077> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2499.782235> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2499782, last_checked:0, failure_count:0, timeout:0
< 2499.782248> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.782273> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.782285> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.789340> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2499.789371> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2499.789380> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2499.789423> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2499.789431> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2499.789457> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2499.789465> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2499.789479> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.789518> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.789534> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.796331> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2499.796550> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.796566> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.817552> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2499.817569> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2499.817618> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2499.817637> libhwkey: 148: data compare failed
< 2499.817671> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2499.817683> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2499.817691> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2499817 
< 2499.817700> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2499.817716> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2499.817905> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2499817, last_checked:0, failure_count:0, timeout:0
< 2499.817918> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.817945> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.817958> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.828312> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2499.828348> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2499.828358> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2499.828402> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2499.828409> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2499.828435> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2499.828442> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2499.828456> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2499.828496> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.828513> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2499.845288> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2499.845554> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2499.845571> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.375863> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2520.375883> trusty_gatekeeper_ta: 89: ReseedRng
< 2520.375895> trusty_gatekeeper_ta: 97: ReseedRng ok
< 2520.375902> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2520.375958> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2520.375977> libhwkey: 148: data compare failed
< 2520.376014> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2520.376026> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2520.376033> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2520376 
< 2520.376042> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2520.376060> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2520.376238> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2520376, last_checked:0, failure_count:0, timeout:0
< 2520.376252> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.376279> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.376293> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.383526> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2520.383564> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2520.383574> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2520.383619> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2520.383626> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2520.383652> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2520.383660> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2520.383674> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.383716> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.383734> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.388632> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2520.388881> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.388897> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.405518> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2520.405536> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2520.405583> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2520.405601> libhwkey: 148: data compare failed
< 2520.405634> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2520.405645> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2520.405652> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2520405 
< 2520.405661> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2520.405678> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2520.405833> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2520405, last_checked:0, failure_count:0, timeout:0
< 2520.405847> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.405872> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.405885> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.413485> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2520.413516> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2520.413525> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2520.413568> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2520.413575> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2520.413601> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2520.413608> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2520.413621> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.413661> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.413676> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.431338> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2520.431576> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.431591> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.442569> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2520.442587> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2520.442634> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2520.442653> libhwkey: 148: data compare failed
< 2520.442688> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2520.442699> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2520.442706> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2520442 
< 2520.442715> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2520.442732> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2520.442892> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2520442, last_checked:0, failure_count:0, timeout:0
< 2520.442906> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.442933> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.442947> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.489417> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2520.489450> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2520.489459> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2520.489505> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2520.489512> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2520.489539> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2520.489546> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2520.489560> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2520.489602> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.489618> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2520.501550> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2520.501795> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2520.501811> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.231612> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2574.231632> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2574.231688> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2574.231710> libhwkey: 148: data compare failed
< 2574.231747> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2574.231758> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2574.231766> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2574231 
< 2574.231775> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2574.231793> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2574.232058> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2574231, last_checked:0, failure_count:0, timeout:0
< 2574.232075> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2574.232104> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.232117> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.241719> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2574.241755> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2574.241764> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2574.241811> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2574.241819> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2574.241847> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2574.241854> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2574.241868> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2574.241909> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.241926> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.247684> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2574.247904> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.247921> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.260400> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2574.260420> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2574.260477> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2574.260499> libhwkey: 148: data compare failed
< 2574.260537> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2574.260549> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2574.260557> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2574260 
< 2574.260566> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2574.260583> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2574.260889> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2574260, last_checked:0, failure_count:0, timeout:0
< 2574.260910> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2574.260949> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.260964> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.268171> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2574.268199> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2574.268208> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2574.268249> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2574.268256> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2574.268281> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2574.268288> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2574.268302> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2574.268339> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.268354> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.275051> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2574.275286> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.275301> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.303102> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2574.303119> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2574.303168> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2574.303188> libhwkey: 148: data compare failed
< 2574.303222> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2574.303233> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2574.303241> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2574303 
< 2574.303250> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2574.303266> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2574.303426> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2574303, last_checked:0, failure_count:0, timeout:0
< 2574.303439> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2574.303466> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.303480> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.326249> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2574.326279> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2574.326289> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2574.326331> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2574.326339> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2574.326364> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2574.326372> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2574.326385> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2574.326425> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.326441> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2574.343771> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2574.344303> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2574.344323> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.800337> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2643.800357> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2643.800410> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2643.800431> libhwkey: 148: data compare failed
< 2643.800469> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2643.800480> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2643.800489> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2643800 
< 2643.800499> trusty_gatekeeper_ta: 147: Verify uid:100000, sid:9159674682746387616
< 2643.800521> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2643.800782> trusty_gatekeeper_ta: 353: ThrottleRequest uid:100000, timestamp:2643800, last_checked:0, failure_count:0, timeout:0
< 2643.800798> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2643.800828> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.800845> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.809937> trusty_gatekeeper_ta: 214: salted[88]:142  salted[0]:100
< 2643.809973> trusty_gatekeeper_ta: 217: signature[31]:96  signature[0]:173
< 2643.809984> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2643.810030> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2643.810039> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2643.810067> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2643.810077> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2643.810092> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2643.810134> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.810152> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.815816> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2643.816030> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.816047> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.831514> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2643.831535> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2643.831591> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2643.831613> libhwkey: 148: data compare failed
< 2643.831651> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2643.831662> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2643.831670> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2643831 
< 2643.831679> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2643.831696> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2643.831871> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2643831, last_checked:0, failure_count:0, timeout:0
< 2643.831884> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2643.831913> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.831927> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.839074> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2643.839112> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2643.839121> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2643.839170> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2643.839178> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2643.839204> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2643.839211> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2643.839225> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2643.839265> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.839281> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.856229> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2643.856467> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.856483> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.868346> trusty_gatekeeper_ta: 127: handle_request  GK_VERIFY
< 2643.868364> trusty_gatekeeper_ta: 132: DeriveMasterKey
< 2643.868416> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
< 2643.868438> libhwkey: 148: data compare failed
< 2643.868473> trusty_gatekeeper_ta: 158: Master[31]:192  Master[0]:26
< 2643.868484> trusty_gatekeeper_ta: 120: Verify >>>>>>>>>>>
< 2643.868492> trusty_gatekeeper_ta: 248: GetMillisecondsSinceBoot  time(ms):2643868 
< 2643.868501> trusty_gatekeeper_ta: 147: Verify uid:0, sid:4912068877405679931
< 2643.868517> trusty_gatekeeper_ta: 402: GetSecureFailureRecord
< 2643.868797> trusty_gatekeeper_ta: 353: ThrottleRequest uid:0, timestamp:2643868, last_checked:0, failure_count:0, timeout:0
< 2643.868814> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2643.868861> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.868874> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.900355> trusty_gatekeeper_ta: 214: salted[56]:112  salted[0]:107
< 2643.900389> trusty_gatekeeper_ta: 217: signature[31]:231  signature[0]:37
< 2643.900398> trusty_gatekeeper_ta: 173: GetAuthTokenKey
< 2643.900444> trusty_gatekeeper_ta: 175: keymaster_open rc:1002
< 2643.900452> trusty_gatekeeper_ta: 186: keymaster_get_auth_token_key
< 2643.900478> trusty_gatekeeper_ta: 189: keymaster_get_auth_token_key rc:0 
< 2643.900486> trusty_gatekeeper_ta: 194: leave GetAuthTokenKey auth_token[31]:77
< 2643.900500> trusty_gatekeeper_ta: 541: WriteSecureFailureRecord
< 2643.900541> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.900557> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
< 2643.909202> trusty_gatekeeper_ta: 183: Verify ok <<<<<<<<<<<
< 2643.909445> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2643.909462> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
