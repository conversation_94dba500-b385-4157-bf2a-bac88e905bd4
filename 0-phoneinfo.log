phoneinfo start


cat /proc/cmdline
Wed Jul 23 13:30:52 CST 2025
stack_depot_disable=on kasan.stacktrace=off kvm-arm.mode=protected cgroup_disable=pressure earlycon console=ttyS1,921600n8
       loop.max_part=7 loglevel=1 log_buf_len=1M kpti=0
       firmware_class.path=/odm/firmware,/vendor/firmware
       init=/init root=/dev/ram0 rw printk.devkmsg=on ftrace_dump_on_oops
       swiotlb=1 dummy_hcd.num=0 rcupdate.rcu_expedited=1 rcu_nocbs=0-7 kvm-arm.mode=none lcd_id=ID77666 lcd_name=lcd_ft8057m_sharp_dzx_mipi lcd_base=9caa8000 lcd_size=1600x720 logo_bpix=24  sysdump_magic=80001000 sysdump_re_flag=1  sprdboot.usbmux=0x0 modem=shutdown sprdboot.mode=normal ltemode=lcsfb rfboard.id=0 rfhw.id=41008 crystal=6 32k.less=1 marlin.clktype=1 power.from.extern=0 cpcmdline=end  bootconfig bootcause="Reboot into normal" pwroffcause="device power down" charge.shutdown_rtc_time=1753246106  charge.charge_cycle=202  charge.basp=-1  charge.total_mah=5028000   sprdboot.wdten=e551  sprdboot.dswdten=enabled  sprdboot.dvfs_set=0x0,0,0 sprdboot.slot_suffix=_a FlashID=593159313238 sprdboot.flash=emmc  ro.product.name=A601N 


cat /proc/version
[aaudio.mmap_exclusive_policy]: [2]
[aaudio.mmap_policy]: [2]
[af.media.systemready.state]: [true]
[apex.all.ready]: [true]
[apr.flag.bootmode]: [1]
[bluetooth.device.class_of_device]: [90,2,12]
[bluetooth.device.default_name]: [SPRD_V_Common]
[bluetooth.profile.a2dp.source.enabled]: [true]
[bluetooth.profile.asha.central.enabled]: [true]
[bluetooth.profile.avrcp.target.enabled]: [true]
[bluetooth.profile.bas.client.enabled]: [true]
[bluetooth.profile.gatt.enabled]: [true]
[bluetooth.profile.hfp.ag.enabled]: [true]
[bluetooth.profile.hid.device.enabled]: [true]
[bluetooth.profile.hid.host.enabled]: [true]
[bluetooth.profile.map.server.enabled]: [true]
[bluetooth.profile.opp.enabled]: [true]
[bluetooth.profile.pan.nap.enabled]: [true]
[bluetooth.profile.pan.panu.enabled]: [true]
[bluetooth.profile.pbap.server.enabled]: [true]
[bootreceiver.enable]: [1]
[build.version.extensions.ad_services]: [16]
[build.version.extensions.r]: [16]
[build.version.extensions.s]: [16]
[build.version.extensions.t]: [16]
[build.version.extensions.u]: [16]
[build.version.extensions.v]: [16]
[cache_key.bluetooth.bluetooth_adapter_get_connection_state]: [-8874196796414621290]
[cache_key.bluetooth.bluetooth_adapter_get_profile_connection_state]: [-8874196796414621289]
[cache_key.bluetooth.bluetooth_adapter_get_state]: [-8874196796414621288]
[cache_key.bluetooth.bluetooth_adapter_is_offloaded_filtering_supported]: [-8874196796414621294]
[cache_key.bluetooth.bluetooth_device_get_bond_state]: [-8874196796414621286]
[cache_key.bluetooth.bluetooth_map_get_connection_state]: [-8874196796414621291]
[cache_key.bluetooth.bluetooth_sap_get_connection_state]: [-8874196796414621295]
[cache_key.display_info]: [-4577724655091713393]
[cache_key.get_packages_for_uid]: [-4577724655091713457]
[cache_key.has_system_feature]: [-4577724655091714885]
[cache_key.is_compat_change_enabled]: [-4577724655091713402]
[cache_key.is_interactive]: [-4577724655091713416]
[cache_key.is_power_save_mode]: [-4577724655091714482]
[cache_key.is_user_unlocked]: [-4577724655091714811]
[cache_key.location_enabled]: [-4577724655091714869]
[cache_key.package_info]: [-4577724655091713401]
[cache_key.system_server.account_user_data]: [-4577724655091713719]
[cache_key.system_server.accounts_data]: [-4577724655091713844]
[cache_key.system_server.connectionless_stylus_handwriting]: [-4577724655091714744]
[cache_key.system_server.device_policy_manager_caches]: [-4577724655091713694]
[cache_key.system_server.get_credential_type]: [-4577724655091714307]
[cache_key.system_server.get_night_mode]: [-4577724655091714874]
[cache_key.system_server.stylus_handwriting]: [-4577724655091714745]
[cache_key.telephony.phone_account_to_subid]: [7679362848979809831]
[cache_key.telephony.subscription_manager_service]: [7679362848979809833]
[camera.disable_zsl_mode]: [1]
[dalvik.vm.appimageformat]: [lz4]
[dalvik.vm.dex2oat-Xms]: [64m]
[dalvik.vm.dex2oat-Xmx]: [512m]
[dalvik.vm.dex2oat-cpu-set]: [0,1,2,3]
[dalvik.vm.dex2oat-max-image-block-size]: [524288]
[dalvik.vm.dex2oat-minidebuginfo]: [true]
[dalvik.vm.dex2oat-resolve-startup-strings]: [true]
[dalvik.vm.dex2oat-threads]: [4]
[dalvik.vm.dex2oat64.enabled]: [true]
[dalvik.vm.dexopt.secondary]: [true]
[dalvik.vm.dexopt.thermal-cutoff]: [2]
[dalvik.vm.enable_pr_dexopt]: [true]
[dalvik.vm.finalizer-timeout-ms]: [30000]
[dalvik.vm.heapgrowthlimit]: [256m]
[dalvik.vm.heapmaxfree]: [16m]
[dalvik.vm.heapminfree]: [2m]
[dalvik.vm.heapsize]: [512m]
[dalvik.vm.heapstartsize]: [8m]
[dalvik.vm.heaptargetutilization]: [0.6]
[dalvik.vm.image-dex2oat-Xms]: [64m]
[dalvik.vm.image-dex2oat-Xmx]: [64m]
[dalvik.vm.isa.arm.features]: [default]
[dalvik.vm.isa.arm.variant]: [cortex-a55]
[dalvik.vm.isa.arm64.features]: [default]
[dalvik.vm.isa.arm64.variant]: [cortex-a75]
[dalvik.vm.madvise.artfile.size]: [4294967295]
[dalvik.vm.madvise.odexfile.size]: [104857600]
[dalvik.vm.madvise.vdexfile.size]: [104857600]
[dalvik.vm.minidebuginfo]: [true]
[dalvik.vm.thread-suspend-timeout-ms]: [40000]
[dalvik.vm.usap_pool_enabled]: [false]
[dalvik.vm.usap_pool_refill_delay_ms]: [3000]
[dalvik.vm.usap_pool_size_max]: [3]
[dalvik.vm.usap_pool_size_min]: [1]
[dalvik.vm.usap_refill_threshold]: [1]
[dalvik.vm.useartservice]: [true]
[dalvik.vm.usejit]: [true]
[debug.atrace.tags.enableflags]: [0]
[debug.force_rtl]: [false]
[debug.fwk.enable_adpf_cpu_hint]: [false]
[debug.hwui.skia_tracing_enabled]: [false]
[debug.hwui.skia_use_perfetto_track_events]: [false]
[debug.hwui.use_hint_manager]: [true]
[debug.perfetto.sdk_sysprop_guard_generation]: [0]
[debug.renderengine.backend]: [skiaglthreaded]
[debug.renderengine.skia_tracing_enabled]: [false]
[debug.renderengine.skia_use_perfetto_track_events]: [false]
[debug.sf.auto_latch_unsignaled]: [false]
[debug.sf.enable_adpf_cpu_hint]: [true]
[debug.sf.enable_changezorder_flag]: [1]
[debug.sf.enable_gl_backpressure]: [false]
[debug.sf.enable_gpu_security]: [1]
[debug.sf.enable_skip_rotation]: [1]
[debug.sf.high_fps_late_app_phase_offset_ns]: [-4000000]
[debug.sf.high_fps_late_sf_phase_offset_ns]: [-3000000]
[debug.sf.hwc.min.duration]: [3000000]
[debug.sf.long_screencapture_counts]: [10]
[debug.sf.treat_170m_as_sRGB]: [1]
[debug.stagefright.c2inputsurface]: [-1]
[debug.tracing.battery_status]: [3]
[debug.tracing.device_state]: [0:DEFAULT]
[debug.tracing.mcc]: [460]
[debug.tracing.mnc]: [1]
[debug.tracing.plug_type]: [0]
[debug.tracing.screen_brightness]: [0.4015748]
[debug.tracing.screen_state]: [2]
[dev.bootcomplete]: [1]
[dev.mnt.blk.blackbox]: [mmcblk0p48]
[dev.mnt.blk.cache]: [mmcblk0p47]
[dev.mnt.blk.data]: [mmcblk0p74]
[dev.mnt.blk.data.user.0]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.cur_profiles]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_ce.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_ce.null.0]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_de.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.misc_ce.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.misc_de.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.ref_profiles]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.storage_area]: [mmcblk0p74]
[dev.mnt.blk.metadata]: [mmcblk0p51]
[dev.mnt.blk.mnt.vendor]: [mmcblk0p1]
[dev.mnt.blk.odm]: [mmcblk0p46]
[dev.mnt.blk.product]: [mmcblk0p46]
[dev.mnt.blk.product.priv-app.Payjoy]: [mmcblk0p46]
[dev.mnt.blk.root]: [mmcblk0p46]
[dev.mnt.blk.system_dlkm]: [mmcblk0p46]
[dev.mnt.blk.system_ext]: [mmcblk0p46]
[dev.mnt.blk.vendor]: [mmcblk0p46]
[dev.mnt.blk.vendor_dlkm]: [mmcblk0p46]
[dev.mnt.dev.blackbox]: [mmcblk0p48]
[dev.mnt.dev.cache]: [mmcblk0p47]
[dev.mnt.dev.data]: [dm-50]
[dev.mnt.dev.data.user.0]: [dm-50]
[dev.mnt.dev.data_mirror.cur_profiles]: [dm-50]
[dev.mnt.dev.data_mirror.data_ce.null]: [dm-50]
[dev.mnt.dev.data_mirror.data_ce.null.0]: [dm-50]
[dev.mnt.dev.data_mirror.data_de.null]: [dm-50]
[dev.mnt.dev.data_mirror.misc_ce.null]: [dm-50]
[dev.mnt.dev.data_mirror.misc_de.null]: [dm-50]
[dev.mnt.dev.data_mirror.ref_profiles]: [dm-50]
[dev.mnt.dev.data_mirror.storage_area]: [dm-50]
[dev.mnt.dev.metadata]: [mmcblk0p51]
[dev.mnt.dev.mnt.vendor]: [mmcblk0p1]
[dev.mnt.dev.odm]: [dm-10]
[dev.mnt.dev.product]: [dm-11]
[dev.mnt.dev.product.priv-app.Payjoy]: [dm-10]
[dev.mnt.dev.root]: [dm-7]
[dev.mnt.dev.system_dlkm]: [dm-13]
[dev.mnt.dev.system_ext]: [dm-8]
[dev.mnt.dev.vendor]: [dm-9]
[dev.mnt.dev.vendor_dlkm]: [dm-12]
[dev.mnt.rootdisk.blackbox]: [mmcblk0]
[dev.mnt.rootdisk.cache]: [mmcblk0]
[dev.mnt.rootdisk.data]: [mmcblk0]
[dev.mnt.rootdisk.data.user.0]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.cur_profiles]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_ce.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_ce.null.0]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_de.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.misc_ce.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.misc_de.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.ref_profiles]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.storage_area]: [mmcblk0]
[dev.mnt.rootdisk.metadata]: [mmcblk0]
[dev.mnt.rootdisk.mnt.vendor]: [mmcblk0]
[dev.mnt.rootdisk.odm]: [mmcblk0]
[dev.mnt.rootdisk.product]: [mmcblk0]
[dev.mnt.rootdisk.product.priv-app.Payjoy]: [mmcblk0]
[dev.mnt.rootdisk.root]: [mmcblk0]
[dev.mnt.rootdisk.system_dlkm]: [mmcblk0]
[dev.mnt.rootdisk.system_ext]: [mmcblk0]
[dev.mnt.rootdisk.vendor]: [mmcblk0]
[dev.mnt.rootdisk.vendor_dlkm]: [mmcblk0]
[dumpstate.last_id]: [2]
[graphics.gpu.profiler.support]: [true]
[gsm.client.base]: [android-tcl]
[gsm.current.phone-type]: [1,1]
[gsm.network.type]: [LTE,Unknown]
[gsm.operator.alpha]: [China Unicom,]
[gsm.operator.iso-country]: [cn,]
[gsm.operator.isroaming]: [false,false]
[gsm.operator.numeric]: [46001,]
[gsm.sim.operator.alpha]: [China Unicom,]
[gsm.sim.operator.iso-country]: [cn,]
[gsm.sim.operator.numeric]: [46001,]
[gsm.sim.state]: [LOADED,ABSENT]
[gsm.sys.sim.rtt.allowed]: [0,0]
[gsm.sys.sim.volte.allowedplmn]: [1,0]
[gsm.sys.volte.state]: [1,0]
[gsm.sys.vowifi.state]: [0,0]
[gsm.version.baseband]: [4G_MODEM_22B_W24.36.3|qogirl6_modem,4G_MODEM_22B_W24.36.3|qogirl6_modem]
[gsm.version.ril-impl]: [android reference-ril 1.0]
[init.svc.aconfigd]: [stopped]
[init.svc.aconfigd-mainline-init]: [stopped]
[init.svc.aconfigd-platform-init]: [stopped]
[init.svc.adbd]: [running]
[init.svc.apexd]: [stopped]
[init.svc.apexd-bootstrap]: [stopped]
[init.svc.apexd-snapshotde]: [stopped]
[init.svc.art_boot]: [stopped]
[init.svc.audio_parameter_parser_service]: [running]
[init.svc.audio_tunning_service]: [running]
[init.svc.audioserver]: [running]
[init.svc.bootanim]: [stopped]
[init.svc.boringssl_self_test32]: [stopped]
[init.svc.boringssl_self_test32_vendor]: [stopped]
[init.svc.boringssl_self_test64]: [stopped]
[init.svc.boringssl_self_test64_vendor]: [stopped]
[init.svc.boringssl_self_test_apex32]: [stopped]
[init.svc.boringssl_self_test_apex64]: [stopped]
[init.svc.bpfloader]: [stopped]
[init.svc.cameraserver]: [running]
[init.svc.create_splloader_dual_slot_byname_path]: [stopped]
[init.svc.credstore]: [running]
[init.svc.deletecorefile]: [stopped]
[init.svc.derive_classpath]: [stopped]
[init.svc.derive_sdk]: [stopped]
[init.svc.dmesgd]: [stopped]
[init.svc.drm]: [running]
[init.svc.ext_data]: [running]
[init.svc.fwklog]: [stopped]
[init.svc.gatekeeperd]: [running]
[init.svc.gpsd]: [running]
[init.svc.gpu]: [running]
[init.svc.heapprofd]: [stopped]
[init.svc.hidl_memory]: [running]
[init.svc.hwservicemanager]: [running]
[init.svc.idmap2d]: [stopped]
[init.svc.ims_bridged]: [running]
[init.svc.incidentd]: [running]
[init.svc.insmod-sh]: [stopped]
[init.svc.installd]: [running]
[init.svc.keystore2]: [running]
[init.svc.linkturbonative]: [running]
[init.svc.lmkd]: [running]
[init.svc.logd]: [running]
[init.svc.logd-auditctl]: [stopped]
[init.svc.logd-reinit]: [stopped]
[init.svc.media]: [running]
[init.svc.media.swcodec]: [running]
[init.svc.media.unisoc.codec2]: [running]
[init.svc.mediadrm]: [running]
[init.svc.mediaextractor]: [running]
[init.svc.mediametrics]: [running]
[init.svc.minidumpd]: [stopped]
[init.svc.miscdata_hal_service]: [running]
[init.svc.misctrl]: [stopped]
[init.svc.netd]: [running]
[init.svc.nfc_hal_service.tms.aidl]: [running]
[init.svc.nhmonitor]: [stopped]
[init.svc.odsign]: [stopped]
[init.svc.perfetto_persistent_sysui_tracing_for_bugreport]: [stopped]
[init.svc.phasecheckserver]: [running]
[init.svc.poweronlog]: [stopped]
[init.svc.prng_seeder]: [running]
[init.svc.remotedisplay]: [running]
[init.svc.rss_hwm_reset]: [stopped]
[init.svc.servicemanager]: [running]
[init.svc.slogmodem]: [running]
[init.svc.sprd_networkcontrol]: [stopped]
[init.svc.srmi_proxyd]: [running]
[init.svc.statsd]: [running]
[init.svc.storaged]: [running]
[init.svc.surfaceflinger]: [running]
[init.svc.swappiness-sh]: [stopped]
[init.svc.systemDebuggerd]: [stopped]
[init.svc.system_suspend]: [running]
[init.svc.tombstoned]: [running]
[init.svc.tool_service]: [running]
[init.svc.traced]: [running]
[init.svc.traced_perf]: [stopped]
[init.svc.traced_probes]: [running]
[init.svc.ueventd]: [running]
[init.svc.ufs_ffu]: [stopped]
[init.svc.uniber]: [running]
[init.svc.unionpnp_service]: [running]
[init.svc.uniresctlopt]: [running]
[init.svc.uniview]: [running]
[init.svc.update_engine]: [running]
[init.svc.update_verifier]: [stopped]
[init.svc.usbd]: [stopped]
[init.svc.vndservicemanager]: [running]
[init.svc.vold]: [running]
[init.svc.watchdogd]: [stopped]
[init.svc.wificond]: [running]
[init.svc.wpa_supplicant]: [running]
[init.svc.ylog]: [running]
[init.svc.yloglite]: [stopped]
[init.svc.zramwb-sh]: [stopped]
[init.svc.zygote]: [running]
[init.svc.zygote_secondary]: [running]
[log.tag.APM_AudioPolicyManager]: [D]
[log.tag.stats_log]: [I]
[logd.ready]: [true]
[net.bt.name]: [Android]
[nfc.fw.downloadmode_force]: [0]
[oem_trusted_authority]: [com.sprd.android.USCPhotosProvider.providers.SpecialTypesProvider]
[oem_trusted_certificate]: [E3AAF983CBE30BA04FC5A3AF4605F9A5315A8FE1]
[partition.odm.verified.check_at_most_once]: [0]
[partition.product.verified.check_at_most_once]: [0]
[partition.system.verified.check_at_most_once]: [0]
[partition.system_dlkm.verified]: [2]
[partition.system_dlkm.verified.check_at_most_once]: [0]
[partition.system_dlkm.verified.hash_alg]: [sha256]
[partition.system_dlkm.verified.root_digest]: [77de4c265d9107184d177281c0d9da46d4f5bd745e51270f94aad83367114103]
[partition.system_ext.verified.check_at_most_once]: [0]
[partition.vendor.verified.check_at_most_once]: [0]
[partition.vendor_dlkm.verified]: [2]
[partition.vendor_dlkm.verified.check_at_most_once]: [0]
[partition.vendor_dlkm.verified.hash_alg]: [sha256]
[partition.vendor_dlkm.verified.root_digest]: [556ea4573866acde13f7e16ddb53ed5e2e6ed7f80adef845269941e7afd4abbd]
[persist.audio.bigvolume.enabled]: [false]
[persist.audio.bigvolume.switch]: [false]
[persist.device_config.aconfig_flags.accessibility.enable_magnifier_thumbnail]: [false]
[persist.device_config.aconfig_flags.activity_manager_native_boot.modern_queue_enabled]: [true]
[persist.device_config.aconfig_flags.bluetooth.INIT_gd_hal_snoop_logger_filtering]: [true]
[persist.device_config.aconfig_flags.bluetooth.INIT_gd_hal_snoop_logger_socket]: [true]
[persist.device_config.aconfig_flags.bluetooth.audio_policy_ag_enabled]: [true]
[persist.device_config.aconfig_flags.bluetooth.audio_policy_hf_enabled]: [true]
[persist.device_config.aconfig_flags.bluetooth.le_audio_enabled_by_default]: [false]
[persist.device_config.aconfig_flags.bluetooth.location_denylist_advertising_data]: [⊈0016AAFE40/00FFFFFFF0,⊆0016AAFE/00FFFFFF,⊆00FF4C0002/00FFFFFFFF]
[persist.device_config.aconfig_flags.bluetooth.location_denylist_mac]: []
[persist.device_config.aconfig_flags.bluetooth.location_denylist_name]: []
[persist.device_config.aconfig_flags.bluetooth.scan_timeout_millis]: [300000]
[persist.device_config.aconfig_flags.codec_fwk.com.android.media.codec.flags.aidl_hal]: [true]
[persist.device_config.aconfig_flags.companion.enable_context_sync_telecom]: [false]
[persist.device_config.aconfig_flags.configuration.beta_launch]: [false]
[persist.device_config.aconfig_flags.configuration.beta_public_launch]: [false]
[persist.device_config.aconfig_flags.configuration.demo_flag]: [false]
[persist.device_config.aconfig_flags.configuration.droidfood_launch]: [false]
[persist.device_config.aconfig_flags.configuration.flag]: [true]
[persist.device_config.aconfig_flags.configuration.flag_five]: [false]
[persist.device_config.aconfig_flags.configuration.flag_four]: [false]
[persist.device_config.aconfig_flags.configuration.flag_six]: [false]
[persist.device_config.aconfig_flags.configuration.flag_three]: [false]
[persist.device_config.aconfig_flags.configuration.flag_two]: [false]
[persist.device_config.aconfig_flags.configuration.public_launch]: [false]
[persist.device_config.aconfig_flags.configuration.rescue_party_throttle_duration_min]: [10]
[persist.device_config.aconfig_flags.connectivity.data_stall_consecutive_dns_timeout_threshold]: [5]
[persist.device_config.aconfig_flags.connectivity.dhcp_init_reboot_enabled]: [false]
[persist.device_config.aconfig_flags.connectivity.dhcp_init_reboot_version]: [0]
[persist.device_config.aconfig_flags.connectivity.dhcp_ip_conflict_detect_version]: [0]
[persist.device_config.aconfig_flags.connectivity.dhcp_rapid_commit_enabled]: [false]
[persist.device_config.aconfig_flags.connectivity.dhcp_rapid_commit_version]: [1]
[persist.device_config.aconfig_flags.connectivity.dhcp_restart_configuration_delay]: [1000]
[persist.device_config.aconfig_flags.connectivity.dhcp_server_decline_version]: [0]
[persist.device_config.aconfig_flags.connectivity.ipclient_accept_ipv6_link_local_dns_version]: [0]
[persist.device_config.aconfig_flags.connectivity.ipclient_multicast_ns_version]: [0]
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app11]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app12]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app4]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app5]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app6]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app7]: []
[persist.device_config.aconfig_flags.lmkd_native.thrashing_limit_critical]: [300]
[persist.device_config.aconfig_flags.nearby.nearby_enable_presence_broadcast_legacy]: [false]
[persist.device_config.aconfig_flags.nearby.nearby_nano_app_min_version]: [1]
[persist.device_config.aconfig_flags.nearby.nearby_support_test_app]: [false]
[persist.device_config.aconfig_flags.netd_native.dns_event_subsample_map]: [default:6 0:400 2:110 4:110 7:110]
[persist.device_config.aconfig_flags.netd_native.doh]: [1]
[persist.device_config.aconfig_flags.netd_native.dot_connect_timeout_ms]: [30000]
[persist.device_config.aconfig_flags.netd_native.dot_query_timeout_ms]: [-1]
[persist.device_config.aconfig_flags.netd_native.dot_revalidation_threshold]: [-1]
[persist.device_config.aconfig_flags.netd_native.dot_validation_latency_factor]: [3]
[persist.device_config.aconfig_flags.netd_native.dot_validation_latency_offset_ms]: [100]
[persist.device_config.aconfig_flags.netd_native.dot_xport_unusable_threshold]: [-1]
[persist.device_config.aconfig_flags.netd_native.max_cache_entries]: [640]
[persist.device_config.aconfig_flags.netd_native.max_queries_global]: [2500]
[persist.device_config.aconfig_flags.netd_native.no_retry_after_cancel]: [0]
[persist.device_config.aconfig_flags.netd_native.parallel_lookup]: [0]
[persist.device_config.aconfig_flags.netd_native.sort_nameservers]: [0]
[persist.device_config.aconfig_flags.nnapi_native.current_feature_level]: [7]
[persist.device_config.aconfig_flags.nnapi_native.telemetry_enable]: [false]
[persist.device_config.aconfig_flags.remote_key_provisioning_native.enable_rkpd]: [false]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-mods]: [2]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-mods-server]: [2]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-num-mods]: [100]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-num-mods-server]: [100]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-spec]: [1,5,30,60,600]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-spec-server]: [1,10,60,3600,*]
[persist.device_config.aconfig_flags.runtime_native.metrics.write-to-statsd]: [true]
[persist.device_config.aconfig_flags.runtime_native.use_app_image_startup_cache]: [true]
[persist.device_config.aconfig_flags.runtime_native_boot.disable_lock_profiling]: [false]
[persist.device_config.aconfig_flags.runtime_native_boot.iorap_blacklisted_packages]: []
[persist.device_config.aconfig_flags.runtime_native_boot.iorap_perfetto_enable]: [false]
[persist.device_config.aconfig_flags.runtime_native_boot.iorap_readahead_enable]: [false]
[persist.device_config.aconfig_flags.runtime_native_boot.iorapd_options]: []
[persist.device_config.aconfig_flags.runtime_native_boot.use_generational_gc]: [true]
[persist.device_config.aconfig_flags.storage_native_boot.charging_required]: [false]
[persist.device_config.aconfig_flags.storage_native_boot.dirty_reclaim_rate]: [0.5]
[persist.device_config.aconfig_flags.storage_native_boot.fuse_enabled]: [1]
[persist.device_config.aconfig_flags.storage_native_boot.lifetime_threshold]: [70]
[persist.device_config.aconfig_flags.storage_native_boot.low_battery_level]: [20.0]
[persist.device_config.aconfig_flags.storage_native_boot.min_gc_sleeptime]: [5000]
[persist.device_config.aconfig_flags.storage_native_boot.min_segments_threshold]: [512]
[persist.device_config.aconfig_flags.storage_native_boot.segment_reclaim_weight]: [2.0]
[persist.device_config.aconfig_flags.storage_native_boot.smart_idle_maint_enabled]: [true]
[persist.device_config.aconfig_flags.storage_native_boot.smart_idle_maint_period]: [60]
[persist.device_config.aconfig_flags.storage_native_boot.take_over_get_content]: [false]
[persist.device_config.aconfig_flags.storage_native_boot.target_dirty_ratio]: [80]
[persist.device_config.aconfig_flags.storage_native_boot.transcode_compat_stale]: []
[persist.device_config.aconfig_flags.surface_flinger_native_boot.SkiaTracingFeature__use_skia_tracing]: [false]
[persist.device_config.runtime_native.metrics.reporting-mods]: [2]
[persist.device_config.runtime_native.metrics.reporting-mods-server]: [2]
[persist.device_config.runtime_native.metrics.reporting-num-mods]: [100]
[persist.device_config.runtime_native.metrics.reporting-num-mods-server]: [100]
[persist.device_config.runtime_native.metrics.reporting-spec]: [1,5,30,60,600]
[persist.device_config.runtime_native.metrics.reporting-spec-server]: [1,10,60,3600,*]
[persist.device_config.runtime_native.metrics.write-to-statsd]: [true]
[persist.device_config.runtime_native.use_app_image_startup_cache]: [true]
[persist.device_config.runtime_native_boot.disable_lock_profiling]: [false]
[persist.device_config.runtime_native_boot.iorap_blacklisted_packages]: []
[persist.device_config.runtime_native_boot.iorap_perfetto_enable]: [false]
[persist.device_config.runtime_native_boot.iorap_readahead_enable]: [false]
[persist.device_config.runtime_native_boot.iorapd_options]: []
[persist.device_config.runtime_native_boot.use_generational_gc]: [true]
[persist.netmon.linger]: [20000]
[persist.nhmonitor.enable]: [on]
[persist.radio.emc.source]: [1000]
[persist.radio.is_vonr_enabled_0]: [true]
[persist.radio.multisim.config]: [dsds]
[persist.radio.psregstate]: [1/14,0]
[persist.radio.stk.commandqualifier]: [0,]
[persist.storage.type]: [2]
[persist.sys.3d.calibraion]: [1]
[persist.sys.anti_aging.aging_state]: [0]
[persist.sys.apr.autoupload]: [1]
[persist.sys.apr.enabled]: [0]
[persist.sys.apr.exceptionnode]: [0]
[persist.sys.apr.intervaltime]: [1]
[persist.sys.apr.lifetime]: [0]
[persist.sys.apr.reload]: [0]
[persist.sys.apr.reportlevel]: [2]
[persist.sys.apr.rlchanged]: [800]
[persist.sys.apr.testgroup]: [CSSLAB]
[persist.sys.apr.timechanged]: [180]
[persist.sys.audio.source]: [true]
[persist.sys.bl.clearuserdata]: [true]
[persist.sys.cam3.multi.cam.id]: [2]
[persist.sys.cam3.type]: [back_blur]
[persist.sys.choreographer.activity_cold_start_insert_frame]: [true]
[persist.sys.choreographer.fling_insert_frame]: [true]
[persist.sys.choreographer.pre_animation_load]: [false]
[persist.sys.dalvik.vm.lib.2]: [libart.so]
[persist.sys.displayinset.top]: [0]
[persist.sys.engineer.enabled]: [false]
[persist.sys.extrainfo]: []
[persist.sys.firstboot]: [DONE]
[persist.sys.firstboot_complete]: [1]
[persist.sys.fuse]: [true]
[persist.sys.fuse.passthrough.enable]: [true]
[persist.sys.gms]: [1]
[persist.sys.gps.lpp]: [2]
[persist.sys.heartbeat.enable]: [1]
[persist.sys.lmk.reportkills]: [true]
[persist.sys.locale]: [en-US]
[persist.sys.log.yloglite]: [0]
[persist.sys.navbar.overlay]: [false]
[persist.sys.power.touch]: [1]
[persist.sys.pq.cabc.enabled]: [1]
[persist.sys.pq.dci.enabled]: [1]
[persist.sys.pq.enabled]: [1]
[persist.sys.private_features.enable]: [1]
[persist.sys.pwctl.appidle]: [1]
[persist.sys.pwctl.appidle.force]: [1]
[persist.sys.pwctl.appstats]: [0]
[persist.sys.pwctl.bgclean]: [1]
[persist.sys.pwctl.enable]: [1]
[persist.sys.pwctl.gps]: [1]
[persist.sys.pwctl.gps.onlysave]: [0]
[persist.sys.pwctl.guru]: [1]
[persist.sys.pwctl.onlysave]: [1]
[persist.sys.pwctl.wl]: [1]
[persist.sys.sdcardfs]: [force_on]
[persist.sys.sf.boostpolicy]: [6]
[persist.sys.sf.color_saturation]: [1.0]
[persist.sys.special_datestr]: [W25.60.1]
[persist.sys.ss.enable]: [true]
[persist.sys.ss.habit]: [true]
[persist.sys.ss.hmm]: [true]
[persist.sys.ss.predict]: [false]
[persist.sys.ss.scene]: [true]
[persist.sys.ss.scroll]: [false]
[persist.sys.ss.sr.enable]: [true]
[persist.sys.ss.track]: [true]
[persist.sys.ss.uhc.enable]: [true]
[persist.sys.support.antenna]: [false]
[persist.sys.support.typeC]: [true]
[persist.sys.support.vram]: [true]
[persist.sys.support.vramselect]: [false]
[persist.sys.support.vt]: [true]
[persist.sys.thermal.hightempkiller]: [1]
[persist.sys.time.offset]: [28800000]
[persist.sys.timezone]: [Asia/Shanghai]
[persist.sys.unievent.enabled]: [1]
[persist.sys.unisoc_delayanimationfinish]: [true]
[persist.sys.unisoc_dyn_insert_frame]: [true]
[persist.sys.unisoc_game_boost]: [true]
[persist.sys.unisoc_smart_animation]: [true]
[persist.sys.usb.config]: [adb]
[persist.sys.vilte.socket]: [ap]
[persist.sys.vram_alter_enable]: [false]
[persist.sys.vramenable]: [true]
[persist.sys.vramsize]: [4096M]
[persist.sys.vramstoragelifetime]: [0]
[persist.sys.vramversion]: [3.0]
[persist.sys.wfc.supp_dual_sim]: [true]
[persist.sys.wifi.reset.devpath]: [devices/platform/87000000.cpwcn-btwf/87000000.cpwcn-btwf:sprd-wlan]
[persist.sys.ylog.hcidump]: [0]
[persist.sys.ylog.tcpdump]: [0]
[persist.vendor.sys.isfirstboot]: [0]
[persist.vendor.sys.modem.diag]: [disable]
[persist.vendor.sys.modem.reboot]: [0xff]
[persist.vendor.sys.modem.save_dump]: [1]
[persist.vendor.sys.modemreset]: [1]
[persist.vendor.sys.single.imsstack]: [true]
[persist.vendor.sys.sp.save_dump]: [1]
[persist.vendor.sys.volte.enable]: [true]
[persist.vendor.sys.wcnreset]: [1]
[persist.vendor.sys.wcnstate]: [0]
[persist.wm.extensions.enabled]: [true]
[pm.dexopt.ab-ota]: [speed-profile]
[pm.dexopt.bg-dexopt]: [speed-profile]
[pm.dexopt.boot-after-mainline-update]: [verify]
[pm.dexopt.boot-after-ota]: [verify]
[pm.dexopt.cmdline]: [verify]
[pm.dexopt.first-boot]: [verify]
[pm.dexopt.inactive]: [verify]
[pm.dexopt.install]: [speed-profile]
[pm.dexopt.install-bulk]: [speed-profile]
[pm.dexopt.install-bulk-downgraded]: [verify]
[pm.dexopt.install-bulk-secondary]: [verify]
[pm.dexopt.install-bulk-secondary-downgraded]: [verify]
[pm.dexopt.install-fast]: [skip]
[pm.dexopt.post-boot]: [verify]
[pm.dexopt.shared]: [speed]
[remote_provisioning.enable_rkpd]: [true]
[ril.data.ps.reject]: [0,]
[ril.sys.usb.tether.iface]: [seth_lte0]
[ro.actionable_compatible_property.enabled]: [true]
[ro.adb.secure]: [1]
[ro.allow.mock.location]: [0]
[ro.apex.updatable]: [true]
[ro.appsflyer.preinstall.path]: [/system/etc/pre_install_tiktok.appsflyer]
[ro.audio.bigvolume.music_speaker]: [2]
[ro.audio.bigvolume.voice_earpiece]: [2]
[ro.audio.bigvolume.voice_speaker]: [2]
[ro.baseband]: [unknown]
[ro.bionic.2nd_arch]: [arm]
[ro.bionic.2nd_cpu_variant]: [cortex-a55]
[ro.bionic.arch]: [arm64]
[ro.bionic.cpu_variant]: [cortex-a75]
[ro.board.api_level]: [33]
[ro.board.first_api_level]: [33]
[ro.board.platform]: [ums9230]
[ro.boot.auto.chipid]: [UMS9230-AC]
[ro.boot.auto.efuse]: [UMS9230]
[ro.boot.avb_version]: [1.3]
[ro.boot.boot_devices]: [soc/soc:ap-apb/201d0000.sdio]
[ro.boot.carrier_group]: [OM]
[ro.boot.code]: [A601N]
[ro.boot.ddr_size]: [4096M]
[ro.boot.ddrsize]: [4096M]
[ro.boot.ddrsize.range]: [[4096,5120)]
[ro.boot.dpc]: [false]
[ro.boot.dswdten]: [enabled]
[ro.boot.dtbo_idx]: [11]
[ro.boot.dvfs_set]: [0x0,0,0]
[ro.boot.dynamic_partitions]: [true]
[ro.boot.ecid]: [23724000]
[ro.boot.fingerprint_support]: [0]
[ro.boot.flash.locked]: [1]
[ro.boot.force.user_adb]: [1]
[ro.boot.force_normal_boot]: [1]
[ro.boot.hardware]: [ums9230_6h10]
[ro.boot.lwfq.type]: [1]
[ro.boot.mode]: [normal]
[ro.boot.nfc_support]: [1]
[ro.boot.odm_customized_name]: [A601N]
[ro.boot.oem_tag]: [BR]
[ro.boot.payjoy]: [true]
[ro.boot.pcb_state]: [2]
[ro.boot.pmic.chipid]: [2730]
[ro.boot.product.hardware.sku]: [NFC_dualsim]
[ro.boot.project_name]: [A01]
[ro.boot.segment_efuse_sta]: [0]
[ro.boot.sim_count]: [2]
[ro.boot.slot_suffix]: [_a]
[ro.boot.tclsn]: [A601N12XGKI00D0]
[ro.boot.tclsn2]: [00743A41060C5306]
[ro.boot.tct.platform]: [SPRD]
[ro.boot.vbmeta.avb_version]: [1.1]
[ro.boot.vbmeta.device]: [PARTUUID=1.0]
[ro.boot.vbmeta.device_state]: [locked]
[ro.boot.vbmeta.digest]: [7b3650186b27728f4b597d90f722eac889a5e09b4b83582fe8264542533ac5d6]
[ro.boot.vbmeta.hash_alg]: [sha256]
[ro.boot.vbmeta.size]: [50944]
[ro.boot.vendor.skip.init]: [0]
[ro.boot.verifiedbootstate]: [green]
[ro.boot.veritymode]: [enforcing]
[ro.boot.veritymode.managed]: [yes]
[ro.boot.wdten]: [e551]
[ro.bootimage.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.bootimage.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.bootloader]: [unknown]
[ro.bootmode]: [normal]
[ro.build.ab_update]: [true]
[ro.build.backported_fixes.alias_bitset.long_list]: [2]
[ro.build.characteristics]: [default]
[ro.build.date]: [Mon Jul 21 18:35:06 CST 2025]
[ro.build.date.utc]: [1753094106]
[ro.build.description]: [ums9230_6h10_Natv-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0118:user/release-keys]
[ro.build.flavor]: [ussi_arm64_full-user]
[ro.build.host]: [zby-ThinkStation-P3-Tower]
[ro.build.id]: [AP3A.240905.015.A2]
[ro.build.product]: [ussi_arm64]
[ro.build.tags]: [release-keys]
[ro.build.type]: [user]
[ro.build.user]: [zby]
[ro.build.version.all_codenames]: [REL]
[ro.build.version.base_os]: []
[ro.build.version.codename]: [REL]
[ro.build.version.incremental]: [A0118]
[ro.build.version.known_codenames]: [Base,Base11,Cupcake,Donut,Eclair,Eclair01,EclairMr1,Froyo,Gingerbread,GingerbreadMr1,Honeycomb,HoneycombMr1,HoneycombMr2,IceCreamSandwich,IceCreamSandwichMr1,JellyBean,JellyBeanMr1,JellyBeanMr2,Kitkat,KitkatWatch,Lollipop,LollipopMr1,M,N,NMr1,O,OMr1,P,Q,R,S,Sv2,Tiramisu,UpsideDownCake,VanillaIceCream]
[ro.build.version.min_supported_target_sdk]: [28]
[ro.build.version.preview_sdk]: [0]
[ro.build.version.preview_sdk_fingerprint]: [REL]
[ro.build.version.release]: [15]
[ro.build.version.release_or_codename]: [15]
[ro.build.version.release_or_preview_display]: [15]
[ro.build.version.sdk]: [35]
[ro.build.version.security_patch]: [2025-06-05]
[ro.carrier]: [oversea]
[ro.com.android.dataroaming]: [false]
[ro.com.google.clientidbase]: [android-tcl]
[ro.com.google.clientidbase.ms]: [android-tcl-gep1]
[ro.com.google.clientidbase.vs]: [android-tcl-gep1]
[ro.com.google.gmsversion]: [15_202503]
[ro.config.alarm_alert]: [Atmospheric_Forest-default.mp3]
[ro.config.alarm_vol_default]: [13]
[ro.config.alarm_vol_steps]: [15]
[ro.config.isolated_compilation_enabled]: [true]
[ro.config.media_vol_default]: [13]
[ro.config.media_vol_steps]: [15]
[ro.config.notification_sound]: [Paint.mp3]
[ro.config.ringtone]: [Bloom.mp3,Bloom.mp3]
[ro.config.system_vol_default]: [13]
[ro.config.system_vol_steps]: [15]
[ro.config.vc_call_vol_default]: [5]
[ro.config.vc_call_vol_steps]: [7]
[ro.crypto.metadata.enabled]: [true]
[ro.crypto.state]: [encrypted]
[ro.crypto.type]: [file]
[ro.dalvik.vm.enable_uffd_gc]: [true]
[ro.dalvik.vm.native.bridge]: [0]
[ro.debuggable]: [0]
[ro.ecid]: [23724000]
[ro.force.debuggable]: [0]
[ro.frp.pst]: [/dev/block/by-name/persist]
[ro.fuse.bpf.is_running]: [true]
[ro.hardware]: [ums9230_6h10]
[ro.hardware.audio.primary]: [ums9230]
[ro.hardware.camera]: [unisoc]
[ro.hardware.egl]: [mali]
[ro.hardware.hwcomposer]: [unisoc]
[ro.hardware.sensors]: [unisoc]
[ro.hw_timeout_multiplier]: [2]
[ro.hwui.use_vulkan]: [true]
[ro.kernel.version]: [5.15]
[ro.launcher.desktopgrid]: [true]
[ro.launcher.dynamic]: [false]
[ro.launcher.multimode]: [true]
[ro.launcher.notifbadge.count]: [true]
[ro.llndk.api_level]: [202404]
[ro.lmk.filecache_min_kb]: [153600]
[ro.lmk.kill_timeout_ms]: [200]
[ro.lmk.psi_complete_stall_ms]: [500]
[ro.lmk.stall_limit_critical]: [40]
[ro.lmk.swap_compression_ratio]: [0]
[ro.lmk.swap_free_low_percentage]: [15]
[ro.logd.size.stats]: [64K]
[ro.media.recoderEIS.enabled]: [true]
[ro.media.wfd.rgb.enabled]: [true]
[ro.odm.build.date]: [Mon Jul 21 18:34:55 CST 2025]
[ro.odm.build.date.utc]: [1753094095]
[ro.odm.build.description]: [ums9230_6h10_Natv-user 13 TP1A.220624.014 601NA0118 release-keys]
[ro.odm.build.display.id]: [ums9230_6h10_Natv-user 13 TP1A.220624.014 601NA0118 release-keys]
[ro.odm.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0118:user/release-keys]
[ro.odm.build.version.incremental]: [A0118]
[ro.odm.def.ota.ver]: [601NA0118]
[ro.odm.tct.curef]: [A601N-2*LCBR12]
[ro.oem.key1]: [23724000]
[ro.opa.eligible_device]: [true]
[ro.opengles.version]: [196610]
[ro.postinstall.fstab.prefix]: [/product]
[ro.product.ab_ota_partitions]: [boot,dtbo,init_boot,l_agdsp,l_deltanv,l_fixnv1,l_fixnv2,l_gdsp,l_ldsp,l_modem,mmcblk0boot1,odm,pm_sys,product,sdc,sml,system,system_dlkm,system_ext,teecfg,trustos,uboot,vbmeta,vbmeta_odm,vbmeta_product,vbmeta_system,vbmeta_system_ext,vbmeta_vendor,vendor,vendor_boot,vendor_dlkm]
[ro.product.assistanttouch]: [false]
[ro.product.board]: [A601N]
[ro.product.brand]: [Alcatel]
[ro.product.build.date]: [Mon Jul 21 18:35:00 CST 2025]
[ro.product.build.date.utc]: [1753094100]
[ro.product.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.product.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.product.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0118:user/release-keys]
[ro.product.build.id]: [AP3A.240905.015.A2]
[ro.product.build.tags]: [release-keys]
[ro.product.build.type]: [user]
[ro.product.build.version.incremental]: [A0118]
[ro.product.build.version.release]: [15]
[ro.product.build.version.release_or_codename]: [15]
[ro.product.build.version.sdk]: [35]
[ro.product.cpu.abi]: [arm64-v8a]
[ro.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.product.cpu.abilist64]: [arm64-v8a]
[ro.product.cpu.pagesize.max]: [16384]
[ro.product.device]: [A01]
[ro.product.first_api_level]: [35]
[ro.product.locale]: [en-US]
[ro.product.manufacturer]: [TCL]
[ro.product.model]: [A601N]
[ro.product.name]: [A601N]
[ro.product.odm.brand]: [Alcatel]
[ro.product.odm.device]: [A01]
[ro.product.odm.manufacturer]: [TCL]
[ro.product.odm.model]: [A601N]
[ro.product.odm.name]: [A601N]
[ro.product.product.brand]: [Alcatel]
[ro.product.product.device]: [A01]
[ro.product.product.manufacturer]: [TCL]
[ro.product.product.model]: [A601N]
[ro.product.product.name]: [A601N]
[ro.product.publicname]: [Alcatel A62]
[ro.product.system.brand]: [Alcatel]
[ro.product.system.device]: [A01]
[ro.product.system.manufacturer]: [TCL]
[ro.product.system.model]: [A601N]
[ro.product.system.name]: [A601N]
[ro.product.system_dlkm.brand]: [Alcatel]
[ro.product.system_dlkm.device]: [A01]
[ro.product.system_dlkm.manufacturer]: [TCL]
[ro.product.system_dlkm.model]: [A601N]
[ro.product.system_dlkm.name]: [A601N]
[ro.product.system_ext.brand]: [Alcatel]
[ro.product.system_ext.device]: [A01]
[ro.product.system_ext.manufacturer]: [TCL]
[ro.product.system_ext.model]: [A601N]
[ro.product.system_ext.name]: [A601N]
[ro.product.tcl.dumysuffix]: [DMY]
[ro.product.vendor.brand]: [Alcatel]
[ro.product.vendor.device]: [A01]
[ro.product.vendor.manufacturer]: [TCL]
[ro.product.vendor.model]: [A601N]
[ro.product.vendor.name]: [A601N]
[ro.product.vendor.odm]: [true]
[ro.product.vendor_dlkm.brand]: [Alcatel]
[ro.product.vendor_dlkm.device]: [A01]
[ro.product.vendor_dlkm.manufacturer]: [TCL]
[ro.product.vendor_dlkm.model]: [A601N]
[ro.product.vendor_dlkm.name]: [A601N]
[ro.property_service.version]: [2]
[ro.revision]: [0]
[ro.secure]: [1]
[ro.secure_boot.state]: [1]
[ro.setupwizard.rotation_locked]: [true]
[ro.sf.lcd_density]: [260]
[ro.simlock.onekey.lock]: [0]
[ro.simlock.unlock.autoshow]: [1]
[ro.simlock.unlock.bynv]: [0]
[ro.soc.manufacturer]: [Spreadtrum]
[ro.soc.model]: [T606]
[ro.sprd.pwctl.ultra.message]: [1]
[ro.sprd.superresolution]: [1]
[ro.sr.displaysize.defaultresolution]: [0]
[ro.sr.displaysize.lowresolution]: [1]
[ro.sr.tp_screen_off]: [true]
[ro.support_one_handed_mode]: [true]
[ro.surface_flinger.force_hwc_copy_for_virtual_displays]: [true]
[ro.surface_flinger.game_default_frame_rate_override]: [60]
[ro.surface_flinger.has_HDR_display]: [false]
[ro.surface_flinger.has_wide_color_display]: [false]
[ro.surface_flinger.max_frame_buffer_acquired_buffers]: [3]
[ro.surface_flinger.max_virtual_display_dimension]: [4096]
[ro.surface_flinger.present_time_offset_from_vsync_ns]: [0]
[ro.surface_flinger.primary_display_orientation]: [ORIENTATION_0]
[ro.surface_flinger.protected_contents]: [true]
[ro.surface_flinger.running_without_sync_framework]: [false]
[ro.surface_flinger.set_display_power_timer_ms]: [1000]
[ro.surface_flinger.set_idle_timer_ms]: [4000]
[ro.surface_flinger.set_touch_timer_ms]: [200]
[ro.surface_flinger.start_graphics_allocator_service]: [false]
[ro.surface_flinger.use_content_detection_for_refresh_rate]: [true]
[ro.surface_flinger.use_context_priority]: [true]
[ro.surface_flinger.use_vr_flinger]: [false]
[ro.surface_flinger.vsync_event_phase_offset_ns]: [1000000]
[ro.surface_flinger.vsync_sf_event_phase_offset_ns]: [1000000]
[ro.sys.pwctl.ultrasaving]: [1]
[ro.system.build.date]: [Mon Jul 21 18:35:06 CST 2025]
[ro.system.build.date.utc]: [1753094106]
[ro.system.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.system.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.system.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0118:user/release-keys]
[ro.system.build.id]: [AP3A.240905.015.A2]
[ro.system.build.tags]: [release-keys]
[ro.system.build.type]: [user]
[ro.system.build.version.incremental]: [A0118]
[ro.system.build.version.release]: [15]
[ro.system.build.version.release_or_codename]: [15]
[ro.system.build.version.sdk]: [35]
[ro.system.component.label]: [SYSTEM-Android15--U1.0-W25.29.1]
[ro.system.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.system.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.system.product.cpu.abilist64]: [arm64-v8a]
[ro.system_dlkm.build.date]: [Mon Jul 21 18:35:00 CST 2025]
[ro.system_dlkm.build.date.utc]: [1753094100]
[ro.system_dlkm.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.system_dlkm.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.system_dlkm.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0118:user/release-keys]
[ro.system_dlkm.build.id]: [AP3A.240905.015.A2]
[ro.system_dlkm.build.tags]: [release-keys]
[ro.system_dlkm.build.type]: [user]
[ro.system_dlkm.build.version.incremental]: [A0118]
[ro.system_dlkm.build.version.release]: [15]
[ro.system_dlkm.build.version.release_or_codename]: [15]
[ro.system_dlkm.build.version.sdk]: [35]
[ro.system_ext.build.date]: [Mon Jul 21 18:35:06 CST 2025]
[ro.system_ext.build.date.utc]: [1753094106]
[ro.system_ext.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.system_ext.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0118 release-keys]
[ro.system_ext.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0118:user/release-keys]
[ro.system_ext.build.id]: [AP3A.240905.015.A2]
[ro.system_ext.build.tags]: [release-keys]
[ro.system_ext.build.type]: [user]
[ro.system_ext.build.version.incremental]: [A0118]
[ro.system_ext.build.version.release]: [15]
[ro.system_ext.build.version.release_or_codename]: [15]
[ro.system_ext.build.version.sdk]: [35]
[ro.tct.curef]: [A601N-2*LCBR12]
[ro.tct.trace.bsn]: [A601N12XGKI00D0]
[ro.telephony.default_network]: [9]
[ro.treble.enabled]: [true]
[ro.unipnp.switch]: [true]
[ro.vendor.api_level]: [33]
[ro.vendor.arm.egl.configs.nv12.hal_format]: [0x100]
[ro.vendor.arm.egl.configs.nv12.recordable]: [true]
[ro.vendor.arm.egl.configs.nv16.hal_format]: [0x10]
[ro.vendor.arm.egl.configs.nv16.recordable]: [true]
[ro.vendor.arm.egl.configs.nv21.hal_format]: [0x101]
[ro.vendor.arm.egl.configs.nv21.recordable]: [true]
[ro.vendor.arm.egl.configs.p010.hal_format]: [0x104]
[ro.vendor.arm.egl.configs.p010.recordable]: [true]
[ro.vendor.arm.egl.configs.p210.hal_format]: [0x105]
[ro.vendor.arm.egl.configs.p210.recordable]: [true]
[ro.vendor.arm.egl.configs.q410.hal_format]: [0x10a]
[ro.vendor.arm.egl.configs.q410.recordable]: [true]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.hal_format]: [0x2b]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.hal_format]: [0x16]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.recordable]: [false]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.framebuffer_target]: [true]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.hal_format]: [0x4]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.hal_format]: [0x0]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_yuv_special.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_yuv_special.hal_format]: [0x23]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.hal_format]: [0x2]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.framebuffer_target]: [true]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.hal_format]: [0x1]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.recordable]: [true]
[ro.vendor.arm.egl.configs.y0l2.hal_format]: [0x103]
[ro.vendor.arm.egl.configs.y0l2.recordable]: [true]
[ro.vendor.arm.egl.configs.y210.hal_format]: [0x106]
[ro.vendor.arm.egl.configs.y210.recordable]: [true]
[ro.vendor.arm.egl.configs.y410.hal_format]: [0x107]
[ro.vendor.arm.egl.configs.y410.recordable]: [true]
[ro.vendor.arm.egl.configs.yuv420.hal_format]: [0x108]
[ro.vendor.arm.egl.configs.yuv420.recordable]: [true]
[ro.vendor.arm.egl.configs.yuyv.hal_format]: [0x102]
[ro.vendor.arm.egl.configs.yuyv.recordable]: [true]
[ro.vendor.arm.egl.configs.yvu420.hal_format]: [0x10e]
[ro.vendor.arm.egl.configs.yvu420.recordable]: [true]
[ro.vendor.arm.gralloc.afrc_chroma_usage_flags]: [0x100000000000000,0x80000000000000,0x180000000000000]
[ro.vendor.arm.gralloc.afrc_chroma_usage_mask]: [0x180000000000000]
[ro.vendor.arm.gralloc.afrc_luma_usage_flags]: [0x50000000,0x60000000,0x40000000]
[ro.vendor.arm.gralloc.afrc_luma_usage_mask]: [0x70000000]
[ro.vendor.arm.gralloc.afrc_rgba_usage_flags]: [0x50000000,0x60000000,0x40000000]
[ro.vendor.arm.gralloc.afrc_rgba_usage_mask]: [0x70000000]
[ro.vendor.arm.gralloc.force_back_buffer_usage_flags]: [0x40000000000000]
[ro.vendor.arm.gralloc.force_back_buffer_usage_mask]: [0x40000000000000]
[ro.vendor.arm.gralloc.no_afbc_usage_flags]: [0x20000000]
[ro.vendor.arm.gralloc.no_afbc_usage_mask]: [0x60000000]
[ro.vendor.arm.gralloc.shared_access_usage_flags]: [0x10000000]
[ro.vendor.arm.gralloc.shared_access_usage_mask]: [0x50000000]
[ro.vendor.build.date]: [Mon Jul 21 18:34:53 CST 2025]
[ro.vendor.build.date.utc]: [1753094093]
[ro.vendor.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0118:user/release-keys]
[ro.vendor.build.id]: [TP1A.220624.014]
[ro.vendor.build.tags]: [release-keys]
[ro.vendor.build.type]: [user]
[ro.vendor.build.version.incremental]: [A0118]
[ro.vendor.build.version.release]: [13]
[ro.vendor.build.version.release_or_codename]: [13]
[ro.vendor.build.version.sdk]: [33]
[ro.vendor.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.vendor.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.vendor.product.cpu.abilist64]: [arm64-v8a]
[ro.vendor.version.release]: [601NA0118]
[ro.vendor_dlkm.build.date]: [Mon Jul 21 18:34:55 CST 2025]
[ro.vendor_dlkm.build.date.utc]: [1753094095]
[ro.vendor_dlkm.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0118:user/release-keys]
[ro.vendor_dlkm.build.id]: [TP1A.220624.014]
[ro.vendor_dlkm.build.tags]: [release-keys]
[ro.vendor_dlkm.build.type]: [user]
[ro.vendor_dlkm.build.version.incremental]: [A0118]
[ro.vendor_dlkm.build.version.release]: [13]
[ro.vendor_dlkm.build.version.release_or_codename]: [13]
[ro.vendor_dlkm.build.version.sdk]: [33]
[ro.vndk.version]: [33]
[ro.wifi.channels]: []
[ro.zygote]: [zygote64_32]
[security.perf_harden]: [1]
[selinux.restorecon_recursive]: [/data/misc_ce/0]
[service.sf.present_timestamp]: [1]
[service.wait_for_bootanim]: [1]
[servicemanager.ready]: [true]
[setupwizard.theme]: [glif_v3_light]
[sys.boot.reason]: [reboot,userrequested]
[sys.boot_completed]: [1]
[sys.bootstat.first_boot_completed]: [1]
[sys.debug.fwc]: [0]
[sys.debug.monkey]: [0]
[sys.fota.boot.up]: [false]
[sys.fuse.transcode_enabled]: [true]
[sys.internal.emulated]: [1]
[sys.lmk.minfree_levels]: [18432:0,23040:100,27648:200,32256:250,55296:900,80640:950]
[sys.lmk.reportkills]: [1]
[sys.log.bootimes]: [1]
[sys.log.fwc]: []
[sys.log.wbootimes]: [1]
[sys.nhmonitor.bootmode.allow]: [true]
[sys.oem_unlock_allowed]: [0]
[sys.rss_hwm_reset.on]: [0]
[sys.sysctl.extra_free_kbytes]: [13500]
[sys.system_server.start_count]: [1]
[sys.system_server.start_elapsed]: [10762]
[sys.system_server.start_uptime]: [10762]
[sys.usb.config]: [adb]
[sys.usb.configfs]: [1]
[sys.usb.controller]: [musb-hdrc.1.auto]
[sys.usb.mode]: [normal]
[sys.usb.mtp.disconnected]: [1753246132]
[sys.usb.state]: [adb]
[sys.use_memfd]: [false]
[sys.user.0.ce_available]: [true]
[sys.wifitracing.started]: [1]
[sys.ylog.bootimes]: [1]
[sys.ylog.file]: [/data/ylog/ap/000-0723_133052.ylog]
[sys.ylog.fwc]: [0]
[sys.ylog.path]: [/data/ylog/ap/]
[sys.ylog.version]: [5.0.0]
[sys.ylog.wbootimes]: [1]
[vendor.minidump.crash_reason]: [Normal]
[vendor.sys.ril.agps.active]: [0]
[vold.emulated.volume.ready]: [1]
[vold.has_adoptable]: [1]
[vold.has_compress]: [0]
[vold.has_quota]: [1]
[vold.has_reserved]: [1]


ylogctl q
Wed Jul 23 13:30:56 CST 2025
phoneinfo end
poweron start
Wed Jul 23 13:31:56 CST 2025


uptime
Wed Jul 23 13:31:56 CST 2025
 13:31:56 up 43 min,  0 users,  load average: 17.31, 16.52, 13.79


the log file is 
Wed Jul 23 13:31:56 CST 2025


getprop>/data/ylog/phone.info
Wed Jul 23 13:31:56 CST 2025


chmod 0777 /data/ylog/phone.info
Wed Jul 23 13:31:56 CST 2025
