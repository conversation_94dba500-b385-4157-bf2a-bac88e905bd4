YZIPC02lastlog/kernel.log20CPIZY

[    0.437612][T123@C3] [Native Hang Detect] hang_detect info thread starts.
[    1.907012][T281@C7] sprd_wdh: func_addr = 0xffffffc001cc1f20 , pdg = 0x0 level = 0x0
[    1.908005][T281@C7] sprd_wdh: func_addr = 0xffffffc001cc1f28 , pdg = 0x420000828a4001 level = 0x1
[    1.909114][T281@C7] sprd_wdh: func_addr = 0x79cc1f30 , pdg = 0x0 level = 0x2
[   88.861335][ T1@C1] sysrq: Kill All Tasks
[   88.862325][ T1@C1] sysrq: Changing Loglevel
[   88.862871][ T1@C1] sysrq: Loglevel set to 1
[   89.147598][ T1@C0] reboot: Restarting system with command 'userrequested'
YZIPC02lastlog/error.log20CPIZY

